import click
import random
import os
import shutil
from loguru import logger
from src.browsers import BROWSER_TYPES, BrowserType
from config import DEFAULT_BROWSER_TYPE
from src.controllers import BrowserController
from src.utils.common import parse_indices, get_project_root_path
from time import sleep
from retry import retry
from src.browsers.operations import try_click
from datetime import datetime
from openai import OpenAI
from src.utils.element_util import click_on_image, get_elements, get_element
from src.utils.thread_executor import ThreadExecutor
from src.utils.hhcsv import HHCSV

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]

# 是否开启AI对比
AI_COMPARE = False

logger.add("logs/sentient.log", rotation="10MB", level="SUCCESS")


class QuestionGenerator:
    def __init__(self, client):
        self.client = client

        self.domains = [
            "量子计算",
            "人工智能",
            "区块链",
            "生物技术",
            "神经科技",
            "基因编辑",
            "脑机接口",
            "纳米技术",
            "增强现实",
            "虚拟现实",
            "物联网",
            "边缘计算",
            "云计算",
            "量子通信",
            "自动驾驶",
            "机器人技术",
            "3D打印",
            "可再生能源",
            "太空技术",
            "数字治理",
            "智慧城市",
            "数字身份",
            "隐私保护",
            "网络安全",
            "算法伦理",
            "数字经济",
            "社交媒体技术",
            "教育科技",
            "远程协作技术",
            "数字包容性",
            "认知科学",
            "计算社会学",
            "数字人类学",
            "计算生物学",
            "神经经济学",
            "计算语言学",
            "数字心理学",
            "人机交互",
            "复杂系统科学",
            "元宇宙",
            "去中心化技术",
            "人工智能伦理",
            "后人类技术",
            "生物数字融合",
            "量子人工智能",
            "合成生物学",
            "超级智能",
            "技术奇点",
            "去中心化金融",
            "代币经济",
            "共享经济",
            "平台经济",
            "创意经济",
            "注意力经济",
            "数据经济",
            "数字货币",
            "区块链技术",
            "Web3",
            "NFT",
            "DeFi",
            "DAO",
        ]

    def generate_question(self, model="deepseek-ai/DeepSeek-V3"):
        domain = random.choice(self.domains)

        prompt = f"""设计15个关于{domain}的英文问题：
                            - 基础知识点考察
                            - 实际问题解决
                            - 问题之间有关联性
                            - 避免是非题
                            - 优化和改进思路
                            - 仅返回逗号分隔的问题列表
                            - 不要编号和解释"""
        print(prompt)

        response = self.client.chat.completions.create(
            model=model,
            messages=[{
                "role": "user",
                "content": prompt,
            }],
            stream=False,
        )

        questions = response.choices[0].message.content.split(",")
        unique_questions = list(set(questions))

        return unique_questions


class AI:
    def __init__(self, api_key=None, model=None, base_url=None):
        self.api_key = api_key or os.getenv("AI_API_KEY")
        self.model = model or os.getenv("AI_MODEL")
        self.base_url = base_url or os.getenv("AI_BASE_URL")

        if not self.api_key:
            logger.error("AI_API_KEY 未设置")
            raise Exception("AI_API_KEY 未设置")

        if not self.model:
            logger.error("AI_MODEL 未设置")
            raise Exception("AI_MODEL 未设置")

        if not self.base_url:
            logger.error("AI_BASE_URL 未设置")
            raise Exception("AI_BASE_URL 未设置")

        self.client = OpenAI(
            api_key=self.api_key,
            base_url=self.base_url,
        )

    def generate_question(self):
        try:
            question_generator = QuestionGenerator(self.client)
            return question_generator.generate_question(self.model)
        except Exception as e:
            logger.error(f"生成问题失败: {e}")
            return []

    def compare_answers(self, question, answer1, answer2):
        response = self.client.chat.completions.create(
            model=self.model,
            messages=[{
                "role": "user",
                "content": f"""
                    对于以下问题:
                    "{question}"
                    
                    比较这两个答案:
                    答案1: {answer1}
                    答案2: {answer2}
                    
                    评估重点:
                    1. 技术准确性
                    - 信息的正确性
                    - 科学精确度

                    2. 解释深度
                    - 细节层次
                    - 理解的复杂程度

                    3. 沟通清晰度
                    - 理解的易用性
                    - 逻辑结构

                    评分标准:
                    - 'a' - 答案1明显更优
                    - 'b' - 答案2明显更优
                    - '0' - 两个答案同等优秀
                    - '-1' - 两个答案都不令人满意

                    仅返回: 'a'、'b'、'0' 或 '-1'。
                    """,
            }],
            stream=False,
        )

        try:
            result = response.choices[0].message.content.strip().lower()
            return result if result in ["a", "b", "0", "-1"] else "-1"
        except Exception as e:
            logger.error(f"评分出错: {e}")
            return "-1"


class Sentient:
    def __init__(self, browser_type: BrowserType, id: str):
        self.browser_type = browser_type
        self.id = id
        self.ai = AI()
        self.browser_controller = BrowserController(browser_type, id)
        self.page = self.browser_controller.page

        self._copy_original_csv()

    def _copy_original_csv(self):
        """拷贝原始CSV文件"""
        try:
            original_csv_path = os.path.join(os.path.dirname(__file__), "data.csv.example")

            self.csv_path = os.path.join(
                os.path.dirname(__file__),
                f'votes_{datetime.now().strftime("%Y%m%d")}.csv',
            )

            if not os.path.exists(self.csv_path):
                shutil.copy(original_csv_path, self.csv_path)
        except Exception as e:
            logger.error(f"拷贝CSV文件失败: {e}")

    def _update_remaining_votes(self, remaining_votes):
        """更新CSV文件"""
        try:
            input_csv = HHCSV(self.csv_path)
            input_csv.update_row(
                criteria={"id": str(self.id)},
                updates={"remaining_votes": remaining_votes},
            )
        except Exception as e:
            logger.error(f"更新CSV文件失败: {e}")

    def _update_last_vote_time(self):
        """更新CSV文件"""
        try:
            last_vote_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            input_csv = HHCSV(self.csv_path)
            input_csv.update_row(
                criteria={"id": str(self.id)},
                updates={"last_vote_time": last_vote_time},
            )
        except Exception as e:
            logger.error(f"更新CSV文件失败: {e}")

    def _get_remaining_votes(self):
        """获取剩余投票次数"""
        try:
            input_csv = HHCSV(self.csv_path)
            result = input_csv.query(criteria={"id": str(self.id)})
            return int(result[0].get("remaining_votes"))
        except Exception as e:
            logger.error(f"获取剩余投票次数失败: {e}")
            return -1

    def _get_last_vote_time(self):
        """获取上次投票时间"""
        try:
            input_csv = HHCSV(self.csv_path)
            result = input_csv.query(criteria={"id": str(self.id)})
            return result[0].get("last_vote_time")
        except Exception as e:
            logger.error(f"获取上次投票时间失败: {e}")
            return None

    def _connect_wallet(self, lasted_tab):

        try_count = 6
        for i in range(try_count):

            if "connect" not in lasted_tab.url:
                if lasted_tab.ele("What can I help you with today", timeout=10):
                    logger.success(f"【{self.id}】 钱包已连接")
                    return True

            connect_wallet_button = lasted_tab.ele("x://button[.='Connect Wallet']", timeout=5)
            if connect_wallet_button:
                connect_wallet_button.click()
                lasted_tab.ele("Continue with a wallet").click()
                sleep(2)
                lasted_tab.ele("x://button[@title='okx']").click()
                self.browser_controller.okx_wallet_connect()
                sleep(2)
                continue

            sleep(3)

        logger.error(f"【{self.id}】 连接钱包失败，已尝试{try_count}次")
        return False

    def _check_cf_shield(self, lasted_tab):

        for _ in range(6):

            # 1. 判断是否在Dobby Battle Arena页面
            if "Dobby Battle Arena" in lasted_tab.title:
                return True

            # 2. 判断是否在CF盾页面
            div_ele = lasted_tab.ele("x://div[@class='main-content']/div/div/div")
            iframe = div_ele.sr(
                "x://iframe[contains(@src, 'challenges.cloudflare.com')]",
                timeout=10,
            )
            if iframe:
                try:
                    logger.warning(f"【{self.id}】 在CF盾页面")

                    checkbox = iframe.ele("tag:body").sr("x://input[@type='checkbox']")
                    if not checkbox:
                        raise Exception(f"{self.id} 未找到验证码输入框")
                    checkbox.wait.has_rect(timeout=20)

                    # if os.name == "nt":  # Windows
                    #     image_path = (
                    #         f"{get_project_root_path()}/story/img/check_box2.png"
                    #     )
                    # else:
                    #     image_path = (
                    #         f"{get_project_root_path()}/story/img/check_box.png"
                    #     )
                    # click_on_image(image_path)
                    checkbox.click()
                    sleep(3)
                    if lasted_tab.ele("What can I help you with today"):
                        # import pyautogui
                        # pyautogui.moveTo(200, 200, duration=0.1)
                        return True
                    else:
                        raise Exception(f"{self.id} 未找到验证码输入框")
                except Exception as e:
                    logger.error(f"【{self.id}】 过CF盾失败: {e}")
                    sleep(3)
                    continue

        return False

    def _parse_response(self, response_text):
        import re

        try:
            # 使用正则表达式提取所有 \n0:" 之后的内容
            matches = re.findall(r'\n0:"(.*?)"', response_text, re.DOTALL)

            if matches:
                # 合并所有匹配的内容
                content = " ".join(matches)

                # 移除 \n0:" 和 引号
                content = content.replace('\n0:"', "").replace('"', "")

                # 合并内容并替换换行符为实际换行
                content = content.replace("\\n", "\n").strip()

                # 使用正则表达式去除多余的空格
                content = re.sub(r"\s+", " ", content)

                return content

            logger.warning(f"【{self.id}】 未找到匹配内容: {response_text}")
            return None
        except Exception as e:
            logger.error(f"【{self.id}】 解析OpenAI响应失败: {e}, 输入内容: {response_text}")
            return None

    def get_answers(self, lasted_tab):
        res = lasted_tab.listen.wait(timeout=360, count=2)

        if not res:
            return False

        answers = {}
        for packet in res:
            body = packet.response.raw_body
            post_data = packet.request.postData
            id = post_data.get("id")
            result = self._parse_response(body)
            if not result:
                logger.error(f"【{self.id}】 应答包返回格式异常")
                return False
            answers[id] = result
        return answers

    def _click_button(self, lasted_tab, button_text):
        try_click(lasted_tab, f"x://button[contains(.,'{button_text}')]")

    def _get_used_times(self, lasted_tab):
        for _ in range(3):
            try:
                messages_remaining = lasted_tab.ele("x://p[contains(.,'messages remaining')]", timeout=10)
                if messages_remaining:
                    count_msg = messages_remaining.text.replace("/10 messages remaining", "")
                    return int(count_msg)
                else:
                    logger.error(f"【{self.id}】 获取剩余次数失败")

            except Exception as e:
                logger.error(f"【{self.id}】 获取剩余次数失败: {e}")
                sleep(3)
                continue

        raise Exception(f"【{self.id}】 获取剩余次数失败")

    def _ai_compare_answers(self, question, answer1, answer2):

        # 如果关闭AI对比，则随机返回
        if not AI_COMPARE:
            # 有5%的概率随机返回
            if random.random() < 0.05:
                return random.choice(["a", "b", "0"])

            if len(answer1) > len(answer2):
                return "a"

            if len(answer1) < len(answer2):
                return "b"

            return "0"

        return self.ai.compare_answers(question, answer1, answer2)

    def _can_vote(self):
        """判断是否可以投票"""
        try:
            from datetime import datetime, timedelta

            # 获取缓存中的剩余投票次数
            remaining_votes = self._get_remaining_votes()
            last_vote_time = self._get_last_vote_time()
            logger.info(f"【{self.id}】 剩余投票次数: {remaining_votes}, 上次投票时间: {last_vote_time}")

            # 如果没有投票记录，允许投票
            if not last_vote_time:
                return True

            # 解析最后投票时间
            last_time = datetime.strptime(last_vote_time, "%Y-%m-%d %H:%M:%S")
            now = datetime.now()

            # 检查是否超过24小时
            time_since_last_vote = now - last_time

            # 检查剩余投票次数和时间间隔
            if time_since_last_vote >= timedelta(hours=24) or remaining_votes > 0:
                return True

            return False

        except Exception as e:
            logger.error(f"检查投票权限失败: {e}")
            return False

    def _task_ask(self, lasted_tab):

        # 获取页面中剩余投票次数
        used_times = self._get_used_times(lasted_tab)
        if used_times <= 0:
            logger.success(f"【{self.id}】 剩余次数为0, 投票完成")
            self._update_remaining_votes(used_times)
            return True

        # 生成问题
        questions = self.ai_get_questions()
        if len(questions) == 0:
            logger.error(f"【{self.id}】 生成问题失败")
            return False

        logger.success(f"【{self.id}】 生成问题成功")
        # 再加上序号
        for i, q in enumerate(questions):
            try:
                logger.info(f"【{self.id}】 第{i+1}次问题: {q}")
                if not q:
                    continue

                used_times = self._get_used_times(lasted_tab)
                if used_times <= 0:
                    self._update_remaining_votes(used_times)
                    logger.success(f"【{self.id}】 剩余次数为0, 投票完成")
                    return True

                textarea = lasted_tab.ele("x://textarea")
                textarea.clear(True)
                sleep(random.randint(1, 3))
                textarea.input(q)

                lasted_tab.listen.start("https://dobby-arena.sentient.xyz/api/chat")
                try_click(lasted_tab, "x://button[@type='submit']", timeout=10)
                sleep(1)

                answers = self.get_answers(lasted_tab)
                if not answers:
                    logger.error(f"【{self.id}】 服务器异常, 获取答案失败")
                    click_text = random.choice(["Left is Better", "Right is Better", "Tie"])
                    self._click_button(lasted_tab, click_text)
                    sleep(5)
                    continue

                # logger.info(f"【{self.id}】 第{i+1}次问题的答案: {answers}")

                battle1 = answers.get("battle1")
                battle2 = answers.get("battle2")
                result = self._ai_compare_answers(q, battle1, battle2)
                click_text = ""
                if result == "a":
                    logger.info(f"【{self.id}】 第{i+1}次问题 答案A 更好")
                    click_text = "Left is Better"
                elif result == "b":
                    logger.info(f"【{self.id}】 第{i+1}次问题 答案B 更好")
                    click_text = "Right is Better"
                elif result == "0":
                    logger.info(f"【{self.id}】 第{i+1}次问题 答案A 和 B 质量相近")
                    click_text = "Tie"
                else:
                    logger.info(f"【{self.id}】 第{i+1}次问题 答案A 和 B 都不好")
                    click_text = "Both are Bad"

                self._click_button(lasted_tab, click_text)

                if lasted_tab.ele("Vote submitted successfully"):
                    sleep(random.randint(2, 5))
                    logger.success(f"【{self.id}】第{i+1}次 投票成功")
                    self._update_last_vote_time()
                    continue

                logger.error(f"【{self.id}】 第{i+1}次问题 投票失败")
                continue

            except Exception as e:
                logger.error(f"【{self.id}】 第{i+1}次提问失败: {e}")
                continue

        # 统计下投票剩余次数
        used_times = self._get_used_times(lasted_tab)
        self._update_remaining_votes(used_times)
        return True

    def _baidu_ai(self):
        api_key = os.getenv("BAIDU_API_KEY")
        if not api_key:
            logger.error("BAIDU_API_KEY 未设置")
            return None
        base_url = "https://qianfan.baidubce.com/v2"
        model = "deepseek-v3"
        return AI(api_key=api_key, base_url=base_url, model=model)

    def _siliconflow_ai(self):
        api_key = os.getenv("SILICONFLOW_API_KEY")
        if not api_key:
            logger.error("SILICONFLOW_API_KEY 未设置")
            return None
        base_url = "https://api.siliconflow.cn/v1"
        model = "deepseek-ai/DeepSeek-V3"
        return AI(api_key=api_key, base_url=base_url, model=model)

    def baidu_generate_question(self):
        self.ai = self._baidu_ai()
        if not self.ai:
            logger.error("百度AI未初始化")
            return []
        return self.ai.generate_question()

    def deepseek_generate_question(self):
        return self.ai.generate_question()

    def siliconflow_generate_question(self):
        self.ai = self._siliconflow_ai()
        if not self.ai:
            logger.error("硅流AI未初始化")
            return []
        return self.ai.generate_question()

    def ai_get_questions(self):
        questions = self.deepseek_generate_question()
        if len(questions) == 0:
            questions = self.siliconflow_generate_question()
        if len(questions) == 0:
            questions = self.baidu_generate_question()
        return questions

    @retry(tries=3, delay=1)
    def task(self):
        try:

            # 0. 检查投票权限
            if not self._can_vote():
                logger.success(f"【{self.id}】 投票权限不足")
                return True

            # 1. 初始化浏览器
            self.browser_controller.okx_wallet_login()
            lasted_tab = self.page.new_tab("https://dobby-arena.sentient.xyz/")
            sleep(8)

            # 2. 判断是否在CF盾页面
            result = self._check_cf_shield(lasted_tab)
            if not result:
                logger.error(f"【{self.id}】 过CF盾失败")
                return False

            # 3. 连接钱包
            result = self._connect_wallet(lasted_tab)
            if not result:
                logger.error(f"【{self.id}】 连接钱包失败")
                return False

            # 4. 点击开始
            self._task_ask(lasted_tab)

            return True

        except Exception as e:
            logger.error(f"【{self.id}】 任务执行失败: {e}")
            return False
        finally:
            try:
                self.page.quit()
            except Exception as e:
                logger.error(f"【{self.id}】 关闭页面失败: {e}")

    def _get_today_clicks(self):
        """获取今日已点击次数"""
        return self.today_clicks

    def join_fraction_ai(self):
        #
        url = f"https://discord.com/invite/wEJCeckB9a"
        try:
            tab = self.page.new_tab(url)

            # 检查是否登录
            if get_element(tab, "x://div[text()='Already have an account?']", 3):
                logger.warning(f"{self.id} dc已经登出, 请登录dc")
                return False

            # 点击加群按钮
            join = get_element(tab, "x://button[@type='button']", 5)
            if join:
                join.click()
            else:
                logger.error(f"{self.id} 加群失败, 未找到加入按钮")
                return False

            # 判断是否跳转到进群页面
            if not tab.wait.url_change("onboarding", timeout=10):
                # 检查是否需要处理验证码
                if get_element(tab, "x://div[@aria-label='CAPTCHA']", 3):
                    logger.warning(f"{self.id} 需要处理验证码，请手动处理")
                    return False
                ele = get_element(tab, "x://div[text()='Continue to Discord']/..", 3)
                if ele:
                    ele.click()
                    sleep(2)

            sleep(5)
            eles = get_elements(tab, "x://div[contains(@class, 'reactionInner') and @role='button']", 5)
            if not eles or len(eles) == 0:
                logger.warning(f"{self.id} 点击验证失败，请手动处理")
                return False

            eles[0].click()
            sleep(5)
            logger.success(f"{self.id} 加入 Fraction Ai Discord服务器成功")
            return True
        except Exception as e:
            logger.error(f"{self.id} 加入 Discord 服务器时发生错误: {str(e)}")
            return False

    def join_sentient(self):
        url = f"https://discord.com/invite/sentientfoundation"
        try:
            tab = self.page.new_tab(url)

            # 检查是否登录
            if get_element(tab, "x://div[text()='Already have an account?']", 3):
                logger.warning(f"{self.id} dc已经登出, 请登录dc")
                return False

            # 点击加群按钮
            join = get_element(tab, "x://button[@type='button']", 5)
            if join:
                join.click()
            else:
                logger.error(f"{self.id} 加群失败, 未找到加入按钮")
                return False

            # 判断是否跳转到进群页面
            if not tab.wait.url_change("onboarding", timeout=10):
                # 检查是否需要处理验证码
                if get_element(tab, "x://div[@aria-label='CAPTCHA']", 3):
                    logger.warning(f"{self.id} 需要处理验证码，请手动处理")
                    return False
                ele = get_element(tab, "x://div[text()='Continue to Discord']/..", 3)
                if ele:
                    ele.click()
                    sleep(2)
            sleep(3)
            if tab.wait.ele_displayed("x://div[contains(@class, 'optionButtonWrapper')]", timeout=10):
                sleep(2)
                # 处理三个选择步骤
                self._handle_option_selection(tab, 0)  # 第一步全选
                sleep(3)

            finish = get_element(tab, "x://div[text()='Finish']/parent::button", 5)
            if finish:
                finish.click()

            sleep(5)
            if tab.wait.url_change("@home", timeout=10):
                sleep(2)
                logger.success(f"{self.id} 加sentient群成功")
                return True
            else:
                logger.warning(f"{self.id} 加sentient群失败")
                return False

        except Exception as e:
            logger.error(f"{self.id} 加入 Discord 服务器时发生错误: {str(e)}")
            return False

    def join_dc(self, invite_code):
        url = f" https://discord.com/invite/{invite_code}"
        try:
            tab = self.page.new_tab(url)

            # 检查是否登录
            if get_element(tab, "x://div[text()='Already have an account?']", 3):
                logger.warning(f"{self.id} dc已经登出, 请登录dc")
                return False

            # 点击加群按钮
            join = get_element(tab, "x://button[@type='button']", 5)
            if join:
                join.click()
            else:
                logger.error(f"{self.id} 加群失败, 未找到加入按钮")
                return False

            # 判断是否跳转到进群页面
            if not tab.wait.url_change("onboarding", timeout=10):
                # 检查是否需要处理验证码
                if get_element(tab, "x://div[@aria-label='CAPTCHA']", 3):
                    logger.warning(f"{self.id} 需要处理验证码，请手动处理")
                    return False
                ele = get_element(tab, "x://div[text()='Continue to Discord']/..", timeout=3)
                if ele:
                    ele.click()
                    sleep(2)

            # if tab.wait.ele_displayed("x://div[contains(@class, 'optionButtonWrapper')]", timeout=10):
            #     sleep(2)
            # # 处理三个选择步骤
            # self._handle_option_selection(tab, 0)  # 第一步全选
            # sleep(2)
            # self._handle_option_selection(tab, 0)  # 第二步全选
            # sleep(3)
            # self._handle_option_selection(tab, 1)  # 第三步随机选一个
            # sleep(2)
            #
            # finish = get_element(tab, "x://div[text()='Finish']/parent::button", 5)
            # if finish:
            #     finish.click()

            if tab.wait.url_change("@home", timeout=10):
                sleep(2)
                logger.success(f"{self.id} 加群成功")
                return True
            else:
                logger.warning(f"{self.id} 加群失败")
                return False
        except Exception as e:
            logger.error(f"{self.id} 加入 Discord 服务器时发生错误: {str(e)}")
            return False

    def _handle_option_selection(self, tab, select_count=0):
        """处理选项选择

        Args:
            tab: 浏览器标签页
            select_count: 需要选择的选项数量，0表示全选
        """
        try:
            eles = get_elements(tab, "x://div[contains(@class, 'optionButtonWrapper')]", 5)
            if not eles:
                return

            if select_count == 0:
                # 全选
                for ele in eles:
                    ele.click()
            else:
                # 随机选择指定数量
                selected = random.sample(eles, min(select_count, len(eles)))
                for ele in selected:
                    ele.click()

            sleep(2)
        except Exception as e:
            logger.error(f"{self.id} 处理选项选择时发生错误: {str(e)}")


def _run_task(type, index):
    try:
        browser = Sentient(type, str(index))
        browser.task()
    except Exception as e:
        logger.error(f"{index} 执行任务异常 {e}")


def get_ids_with_votes(indices):
    """根据输入的 ID 列表输出所有 remaining_votes 不为 0 的 ID"""
    try:
        # 确保输入是列表或类似序列类型
        if not isinstance(indices, (list, tuple)):
            raise TypeError("indices must be a list or tuple")

        # 读取CSV文件
        input_csv = HHCSV(
            file_path=os.path.join(
                os.path.dirname(__file__),
                f'votes_{datetime.now().strftime("%Y%m%d")}.csv',
            )
        )
        # 使用 query 方法获取所有数据
        data = input_csv.query()

        # Filter valid IDs and format output
        valid_ids = [int(row["id"]) for row in data if int(row["id"]) in indices and int(row["remaining_votes"]) > 0]
        return ",".join(map(str, valid_ids))
    except Exception as e:
        logger.error(f"获取有效ID失败: {e}")
        return ""


@click.group()
def cli():
    pass


@cli.command("run")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
def run(type, index, workers):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)

        def process_task(index):
            try:
                browser = Sentient(type, str(index))
                return browser.task()
            except Exception as e:
                logger.error(f"账号 {index} 执行任务异常: {e}")
                return False

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=6 * 3600,  # 6小时超时
            retries=3,
            interval=10,
            task_name=f"Sentient-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        results = executor.run_batch(process_task, indices)
        results_fal = get_ids_with_votes(indices)
        logger.info(f"需重新执行任务执行: {results_fal}")
        logger.info(f"任务执行结果: {results}")
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


@cli.command("join")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def join(type, index):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)

        for _index in indices:
            try:
                sentient = Sentient(type, str(_index))
                s_result = sentient.join_sentient()
                f_result = sentient.join_fraction_ai()
                if s_result and f_result:
                    sentient.page.quit()
            except Exception as e:
                logger.error(f"{_index} 加入DC群失败: {e}")
    except Exception as e:
        logger.error(f"执行加群过程出错: {e}")
        raise


# 点击
# python3 examples/sentient/giveaway2.py run -t ads -i 1-10
# python3 examples/sentient/giveaway2.py join -t ads -i 1-10


if __name__ == "__main__":
    cli()
    # _run_task(BrowserType.CHROME, "3")
    # sentient = Sentient(BrowserType.ADS, "134")
    # if sentient.join_sentient() and sentient.join_fraction_ai():
    #     sentient.page.quit()
