import os
import click
import random
from loguru import logger
from src.browsers import BROWSER_TYPES, BrowserType
from config import DEFAULT_BROWSER_TYPE
from src.controllers import BrowserController
from src.utils.common import parse_indices
from time import sleep
from retry import retry
from src.utils.thread_executor import ThreadExecutor

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]
UPLOAD_FILE_PATH = os.getenv("AVATAR_PATH")
from web3 import Web3

RPC_URL = "https://evmrpc-testnet.0g.ai"


class A0GAI:
    def __init__(self, browser_type: BrowserType, id: str):
        self.browser_type = browser_type
        self.id = id
        self.browser_controller = BrowserController(browser_type, id)
        self.page = self.browser_controller.page

    def get_wallet_address(self, lasted_tab):
        try:
            sleep(1)
            js = """
            const avatar = document.querySelector("body > div > div > div > div > div > w3m-button").shadowRoot.querySelector("w3m-account-button").shadowRoot.querySelector("wui-account-button").shadowRoot.querySelector("wui-avatar");
            return avatar ? avatar.getAttribute('address') : null;
            """
            return lasted_tab.run_js(js)
        except Exception as e:
            return None

    # 检查钱包与合约的交互
    def check_latest_transaction(wallet_address, contract_address):
        try:
            # 获取最新的区块号
            web3 = Web3(Web3.HTTPProvider(RPC_URL))
            latest_block = web3.eth.block_number

            # 获取最新的交易
            latest_tx = web3.eth.get_block(latest_block, full_transactions=True).transactions

            # 检查待处理交易
            pending_txs = web3.eth.get_block("pending", full_transactions=True).transactions

            # 合并最新区块的交易和待处理交易
            all_txs = latest_tx + pending_txs

            # 检查与合约的交互
            interactions = []
            for tx in all_txs:
                if tx.to and tx.to.lower() == contract_address.lower() and tx["from"].lower() == wallet_address.lower():
                    interactions.append(tx)

            if interactions:
                print(f"钱包与合约交互过 {len(interactions)} 次")
                for tx in interactions:
                    print(f"交易哈希: {tx.hash.hex()}")
            else:
                print("钱包未与该合约交互过")

        except Exception as e:
            print("检查交易历史失败:", str(e))

    def w3_get_balance(self):
        web3 = Web3(Web3.HTTPProvider(RPC_URL))
        evm_address = self.browser_controller.browser_config.evm_address
        if not evm_address:
            logger.error(f"【{self.id}】 钱包地址为空")
            return 0

        balance = web3.eth.get_balance(evm_address)
        return balance / 10**18

    def connect_wallet(self, lasted_tab):
        # 点击连接钱包按钮
        try:
            wallet_address = self.get_wallet_address(lasted_tab)
            if wallet_address:
                logger.info(f"【{self.id}】 钱包已经登录")
                return True

            connect_button = (
                lasted_tab.ele("tag:w3m-button", timeout=6)
                .sr("tag:w3m-connect-button", timeout=3)
                .sr("x://wui-connect-button[@data-testid='connect-button']", timeout=3)
            )
            if connect_button:
                connect_button.click()
                sleep(1)

                # 点击OKX钱包
                lasted_tab.ele("tag:w3m-modal", timeout=6).sr("tag:wui-flex", timeout=3).ele(
                    "tag:w3m-router", timeout=3
                ).sr("tag:w3m-connect-view", timeout=3).sr("x://wui-list-wallet[@name='OKX Wallet']", timeout=3).click()

                # 点击连接按钮
                self.browser_controller.okx_wallet_connect()

                wallet_address = self.get_wallet_address(lasted_tab)
                if wallet_address:
                    logger.info(f"【{self.id}】 钱包链接成功")
                    return True
                return False
        except Exception as e:
            logger.error(f"【{self.id}】 连接钱包失败: {e}")
            raise e

    def switch_network(self, lasted_tab):
        try:
            js = """
            const network = document.querySelector("body > w3m-modal").shadowRoot.querySelector("wui-flex > wui-card > w3m-router").shadowRoot.querySelector("div > w3m-unsupported-chain-view").shadowRoot.querySelector("wui-flex > wui-flex:nth-child(2) > wui-list-network")
        if(network) {
            network.click()
            }
            """
            lasted_tab.run_js(js)

            sleep(3)

            self.browser_controller.okx_wallet_sign()
        except Exception as e:
            pass

    def upload_file(self, lasted_tab):
        logger.info(f"{self.id} 准备上传图片...")
        file = lasted_tab("tag:input@type=file")
        _path = UPLOAD_FILE_PATH
        _path = _path.replace("*", str(self.id))

        if not os.path.exists(_path):
            logger.warning(f"{self.id} 图片文件不存在,跳过上传: {_path}")
            return False

        file.input(rf"{_path}")

        # 点击上传按钮
        ele = lasted_tab.ele("Upload prepared", timeout=30)
        if not ele:
            logger.error(f"{self.id} 上传失败, prepared error")
            return

        lasted_tab.ele("x://button[.='Upload']", timeout=6).click()

        ele = lasted_tab.ele("Please sign the transaction", timeout=30)
        if not ele:
            logger.error(f"{self.id} 上传失败 sign error")
            return

        sleep(3)
        self.browser_controller.okx_wallet_sign()

        # 点击确认按钮
        ele = lasted_tab.ele("Upload complete", timeout=20)
        if ele:
            logger.success(f"{self.id} 上传成功")
            return True

        logger.error(f"{self.id} 上传失败")
        return False

    @retry(tries=3, delay=1)
    def task_upload(self):

        balance = self.w3_get_balance()
        if balance <= 0.01:
            logger.warning(f"{self.id} 余额 {balance}, 不足以交互...")
            return

        self.browser_controller.okx_wallet_login()
        lasted_tab = self.page.new_tab("https://storagescan-newton.0g.ai/tool")

        for i in range(3):
            try:
                # 连接钱包
                result = self.connect_wallet(lasted_tab)
                if not result:
                    return False

                self.switch_network(lasted_tab)

                # 上传文件
                result = self.upload_file(lasted_tab)
                if not result:
                    return False

            except Exception as e:
                logger.error(f"【{self.id}】 任务失败: {e}")
                lasted_tab.refresh()
                sleep(3)
                continue

        logger.error(f"【{self.id}】 任务失败")
        return False


def _upload_task(type, index):
    try:
        browser = A0GAI(type, str(index))
        return browser.task_upload()
    except Exception as e:
        logger.error(f"{index} 执行任务异常 {e}")
        return False


@click.group()
def cli():
    pass


@cli.command("upload")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=5, help="并发数量")
def upload(type, index, workers):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)

        def process_task(index):
            return _upload_task(type, index)

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=6 * 3600,  # 6小时超时
            retries=1,
            interval=10,
            task_name=f"0G-Upload-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        results = executor.run_batch(process_task, indices)
        logger.info(f"任务执行结果: {results}")
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


# 上传
# python3 examples/0g/giveaway2.py upload -t ads -i 1-10 -w 5

if __name__ == "__main__":
    cli()
    # _upload_task(BrowserType.ADS, "30")
