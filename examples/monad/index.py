import os
import random
import shutil
from datetime import datetime
from time import sleep

import click
from loguru import logger
from retry import retry

from config import DEFAULT_BROWSER_TYPE
from src.biz.nfts2 import NFTs2
from src.browsers import BROWSER_TYPES, BrowserType
from src.controllers import BrowserController
from src.utils.common import parse_indices
from src.utils.data_utils import DataUtil
from src.utils.thread_executor import ThreadExecutor

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]

logger.add("logs/monad.log", rotation="10MB", level="SUCCESS")


class Monad:
    def __init__(self, browser_type: BrowserType, id: str):
        self.id = id
        self.browser_type = browser_type
        self.browser_controller = BrowserController(browser_type, id)
        self.page = self.browser_controller.page
        self._copy_original_csv()

    def _copy_original_csv(self):
        """拷贝原始CSV文件."""
        try:
            original_csv_path = os.path.join(os.path.dirname(__file__), "data.csv.example")

            self.csv_path = os.path.join(
                os.path.dirname(__file__),
                f"votes_{datetime.now().strftime('%Y%m%d')}.csv",
            )

            if not os.path.exists(self.csv_path):
                shutil.copy(original_csv_path, self.csv_path)
        except Exception as e:
            logger.error(f"拷贝CSV文件失败: {e}")

    def _connect_wallet(self, lasted_tab):
        try_count = 6
        for _ in range(try_count):
            if "connect" not in lasted_tab.url:  # noqa: SIM102
                if lasted_tab.ele("What can I help you with today", timeout=10):
                    logger.success(f"【{self.id}】 钱包已连接")
                    return True

            connect_wallet_button = lasted_tab.ele("x://button[.='Connect Wallet']", timeout=5)
            if connect_wallet_button:
                connect_wallet_button.click()
                lasted_tab.ele("Continue with a wallet").click()
                sleep(2)
                lasted_tab.ele("x://button[@title='okx']").click()
                self.browser_controller.okx_wallet_connect()
                sleep(2)
                continue

            sleep(3)

        logger.error(f"【{self.id}】 连接钱包失败，已尝试{try_count}次")
        return False

    @retry(tries=3, delay=1)
    def task(self):
        try:
            # 1. 初始化浏览器
            self.browser_controller.okx_wallet_login()
            lasted_tab = self.page.new_tab("https://testnet.monad.xyz/")
            sleep(3)

            # 3. 连接钱包
            result = self._connect_wallet(lasted_tab)
            if not result:
                logger.error(f"【{self.id}】 连接钱包失败")
                return False

            # 4. 点击开始
            self._task_ask(lasted_tab)

            return True

        except Exception as e:
            logger.error(f"【{self.id}】 任务执行失败: {e}")
            return False
        finally:
            try:
                self.page.quit()
            except Exception as e:
                logger.error(f"【{self.id}】 关闭页面失败: {e}")


def _run_task(type, index):
    try:
        browser = Monad(type, str(index))
        browser.task()
    except Exception as e:
        logger.error(f"{index} 执行任务异常 {e}")


@click.group()
def cli():
    pass


@cli.command("run")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
def run(type, index, workers):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)

        def process_task(index):
            try:
                browser = Monad(type, str(index))
                return browser.task()
            except Exception as e:
                logger.error(f"账号 {index} 执行任务异常: {e}")
                return False

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=6 * 3600,  # 6小时超时
            retries=3,
            interval=10,
            task_name=f"Monad-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        results = executor.run_batch(process_task, indices)
        logger.info(f"任务执行结果: {results}")
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


@cli.command("nfts2")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
def nfts2(type, index, workers):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)
        csv_path = os.path.join(os.path.dirname(__file__), "data.csv")
        data_util = DataUtil(csv_path)

        successful_indices = []
        failed_indices = []

        def process_task(index):
            controller = None
            try:
                controller = BrowserController(type, str(index))
                nft2 = NFTs2(controller)
                data = data_util.get(nft2.address)
                if not data:
                    data_util.add({"address": nft2.address, "index": index, "type": type.value})

                if data.get("nft2"):
                    logger.info(f"账号 {index} 已创建NFT")
                    successful_indices.append(index)
                    return True

                result = nft2.task_create_edition(network="Monad")
                if not result:
                    logger.error(f"账号 {index} 创建NFT失败")
                    failed_indices.append(index)
                    return False
                data_util.update(nft2.address, {"nft2": 1})
                successful_indices.append(index)
                return True
            except Exception as e:
                logger.error(f"账号 {index} 执行任务异常: {e}")
                failed_indices.append(index)
                return False
            finally:
                if controller:
                    controller.close_page()

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=3600,  # 1小时超时
            retries=3,
            interval=10,
            task_name=f"Monad-NFTs2-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        executor.run_batch(process_task, indices)

        # 计算失败的索引
        failed_indices = list(set(indices) - set(successful_indices))
        failed_indices = [str(index) for index in failed_indices]

        # 输出执行结果
        logger.info(f"成功的账号列表: {successful_indices}")
        logger.info(f"失败的账号列表: [{','.join(failed_indices)}]")

    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


# 点击
# python3 examples/monad/giveaway2.py run -t ads -i 1-10
# python3 examples/monad/giveaway2.py nfts2 -t ads -i 1-10


if __name__ == "__main__":
    cli()
    # _run_task(BrowserType.CHROME, "1")
