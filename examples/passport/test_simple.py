#!/usr/bin/env python3
import os
import sys
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from loguru import logger
from src.browsers import BrowserType
from src.controllers import BrowserController

def test_browser_connection():
    """测试浏览器连接"""
    try:
        logger.info("开始测试浏览器连接...")
        
        # 创建浏览器控制器
        browser_controller = BrowserController(BrowserType.CHROME, "1")
        logger.info(f"浏览器控制器创建成功，地址: {browser_controller.browser_config.evm_address}")
        
        # 登录OKX钱包
        logger.info("开始登录OKX钱包...")
        browser_controller.okx_wallet_login()
        logger.success("OKX钱包登录成功")
        
        # 访问Passport网站
        logger.info("访问Passport网站...")
        page = browser_controller.page
        tab = page.new_tab("https://app.passport.xyz/#/dashboard")
        time.sleep(5)
        
        logger.info(f"页面标题: {tab.title}")
        logger.success("网站访问成功")
        
        # 关闭浏览器
        browser_controller.close_page()
        logger.success("测试完成")
        
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        return False

if __name__ == "__main__":
    test_browser_connection()
