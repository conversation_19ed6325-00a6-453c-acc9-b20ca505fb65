import os
import sys
import csv
from threading import Lock
from datetime import datetime
from typing import Optional

from loguru import logger


def get_project_root_path():
    """获取项目根目录路径"""
    return os.path.join(os.path.dirname(__file__), '..', '..')

# Passport数据CSV结构
PASSPORT_DATA_PROPS = [
    "index",
    "wallet_address",
    "browser_type",
    "gmail_verified",
    "discord_verified",
    "nft_verified",
    "humanity_score",
    "timestamp",
    "status",
    "error_message",
]


class SimpleCSV:
    """简化的CSV处理类"""

    def __init__(self, file_path, headers):
        self.file_path = file_path
        self.headers = headers
        self.data = []
        self.lock = Lock()
        self._ensure_file_exists()
        self.load()

    def _ensure_file_exists(self):
        """确保CSV文件存在"""
        if not os.path.exists(self.file_path):
            os.makedirs(os.path.dirname(self.file_path), exist_ok=True)
            with open(self.file_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=self.headers)
                writer.writeheader()

    def load(self):
        """加载CSV数据"""
        with self.lock:
            try:
                with open(self.file_path, 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    self.data = list(reader)
            except Exception as e:
                logger.error(f"加载CSV文件失败: {e}")
                self.data = []

    def save(self):
        """保存CSV数据"""
        with self.lock:
            try:
                with open(self.file_path, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.DictWriter(f, fieldnames=self.headers)
                    writer.writeheader()
                    writer.writerows(self.data)
            except Exception as e:
                logger.error(f"保存CSV文件失败: {e}")

    def add_row(self, row):
        """添加行"""
        with self.lock:
            # 确保行包含所有头部字段
            complete_row = {h: row.get(h, "") for h in self.headers}
            self.data.append(complete_row)
            self.save()

    def query(self, criteria):
        """查询数据"""
        with self.lock:
            result = []
            for row in self.data:
                match = True
                for key, value in criteria.items():
                    if row.get(key) != value:
                        match = False
                        break
                if match:
                    result.append(row)
            return result

    def update_row(self, criteria, updates):
        """更新行"""
        with self.lock:
            updated_count = 0
            for row in self.data:
                match = True
                for key, value in criteria.items():
                    if row.get(key) != value:
                        match = False
                        break
                if match:
                    row.update(updates)
                    updated_count += 1
            if updated_count > 0:
                self.save()
            return updated_count


class PassportDataUtil:
    """Passport.xyz数据管理工具"""
    
    def __init__(self, csv_filename = "passport.csv"):
        """
        初始化数据工具
        
        Args:
            csv_filename: CSV文件名
        """
        self.csv_filename = csv_filename
        self._init_csv()

    def _init_csv(self):
        """初始化CSV文件"""
        try:
            # 设置CSV文件路径
            data_dir = os.path.join(get_project_root_path(), "examples", "passport")
            self._csv_path = os.path.join(data_dir, self.csv_filename)
            self._csv = SimpleCSV(self._csv_path, PASSPORT_DATA_PROPS)
            logger.info(f"初始化Passport数据CSV文件: {self._csv_path}")
        except Exception as e:
            logger.error(f"初始化Passport CSV文件失败: {str(e)}")
            raise

    def list(self):
        """
        获取所有记录
        
        Returns:
            list: 所有记录列表
        """
        try:
            return self._csv.data
        except Exception as e:
            logger.error(f"查询全部Passport数据失败: {str(e)}")
            return []

    def get_by_index(self, index):
        """
        根据index查询记录
        
        Args:
            index: 浏览器索引
            
        Returns:
            dict: 查询结果，如果未找到返回空字典
        """
        try:
            result = self._csv.query({"index": str(index)})
            return result[0] if result else {}
        except Exception as e:
            logger.error(f"查询 index {index} Passport数据失败: {str(e)}")
            return {}

    def get_by_wallet(self, wallet_address):
        """
        根据钱包地址查询记录
        
        Args:
            wallet_address: 钱包地址
            
        Returns:
            list: 查询结果列表
        """
        try:
            result = self._csv.query({"wallet_address": wallet_address})
            return result if result else []
        except Exception as e:
            logger.error(f"查询 {wallet_address} Passport数据失败: {str(e)}")
            return []

    def add(self, data):
        """
        添加新记录
        
        Args:
            data: 要添加的数据字典
        """
        try:
            # 添加时间戳
            if "timestamp" not in data:
                data["timestamp"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            self._csv.add_row(data)
            logger.info(f"添加Passport记录: index={data.get('index')}, wallet={data.get('wallet_address')}")
        except Exception as e:
            logger.error(f"新增Passport数据失败, data={data}, error={str(e)}")

    def update_by_index(self, index, data):
        """
        根据index更新记录
        
        Args:
            index: 浏览器索引
            data: 要更新的数据字典
            
        Returns:
            bool: 更新是否成功
        """
        try:
            criteria = {"index": str(index)}
            # 更新时间戳
            data["timestamp"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            result = self._csv.update_row(criteria, data)
            if result > 0:
                logger.info(f"更新Passport记录: index={index}, data={data}")
            return result > 0
        except Exception as e:
            logger.error(f"更新Passport数据失败, index={index}, data={data}, error={str(e)}")
            return False

    def update_by_wallet(self, wallet_address, data):
        """
        根据钱包地址更新记录
        
        Args:
            wallet_address: 钱包地址
            data: 要更新的数据字典
            
        Returns:
            bool: 更新是否成功
        """
        try:
            criteria = {"wallet_address": wallet_address}
            # 更新时间戳
            data["timestamp"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            result = self._csv.update_row(criteria, data)
            if result > 0:
                logger.info(f"更新Passport记录: wallet={wallet_address}, data={data}")
            return result > 0
        except Exception as e:
            logger.error(f"更新Passport数据失败, wallet={wallet_address}, data={data}, error={str(e)}")
            return False

    def flush(self):
        """刷新数据"""
        try:
            self._csv.load()
        except Exception as e:
            logger.error(f"刷新Passport数据失败, error={str(e)}")
