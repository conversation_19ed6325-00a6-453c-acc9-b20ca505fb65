#!/usr/bin/env python3
import os
import sys
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from loguru import logger
from src.browsers import BrowserType
from src.controllers import BrowserController
from src.utils.element_util import get_element

def test_passport_elements():
    """测试Passport页面元素"""
    try:
        logger.info("开始测试Passport页面元素...")
        
        # 创建浏览器控制器
        browser_controller = BrowserController(BrowserType.CHROME, "1")
        logger.info(f"浏览器控制器创建成功，地址: {browser_controller.browser_config.evm_address}")
        
        # 登录OKX钱包
        logger.info("开始登录OKX钱包...")
        browser_controller.okx_wallet_login()
        logger.success("OKX钱包登录成功")
        
        # 访问Passport网站
        logger.info("访问Passport网站...")
        page = browser_controller.page
        tab = page.new_tab("https://app.passport.xyz/#/dashboard")
        time.sleep(10)  # 等待页面完全加载
        
        logger.info(f"页面标题: {tab.title}")
        
        # 查找连接钱包按钮
        logger.info("查找连接钱包按钮...")
        connect_buttons = [
            "x://button[contains(text(),'Connect')]",
            "x://button[contains(text(),'连接')]", 
            "x://button[contains(text(),'Wallet')]",
            "x://*[contains(text(),'Connect Wallet')]",
            "x://*[contains(text(),'Connect')]"
        ]
        
        for selector in connect_buttons:
            element = get_element(tab, selector, timeout=3)
            if element:
                logger.success(f"找到连接按钮: {selector} - 文本: {element.text}")
            else:
                logger.info(f"未找到: {selector}")
        
        # 查找已连接的钱包地址
        logger.info("查找钱包地址...")
        wallet_selectors = [
            "x://*[contains(text(),'0x')]",
            "x://*[contains(@class,'address')]",
            "x://*[contains(@class,'wallet')]"
        ]
        
        for selector in wallet_selectors:
            elements = tab.eles(selector)
            for elem in elements:
                if "0x" in elem.text and len(elem.text) > 10:
                    logger.success(f"找到钱包地址: {elem.text}")
        
        # 查找认证相关元素
        logger.info("查找认证元素...")
        auth_selectors = [
            "x://*[contains(text(),'Google')]",
            "x://*[contains(text(),'Gmail')]", 
            "x://*[contains(text(),'Discord')]",
            "x://*[contains(text(),'NFT')]",
            "x://*[contains(text(),'Verify')]",
            "x://*[contains(text(),'Connect')]"
        ]
        
        for selector in auth_selectors:
            elements = tab.eles(selector)
            for elem in elements:
                logger.info(f"认证元素: {selector} - 文本: {elem.text}")
        
        # 查找分数显示
        logger.info("查找分数显示...")
        score_selectors = [
            "x://*[contains(text(),'Unique Humanity Score')]",
            "x://*[contains(text(),'Score')]",
            "x://*[contains(@class,'score')]",
            "x://*[contains(text(),'13.7')]",
            "x://*[contains(text(),'.')]"
        ]
        
        for selector in score_selectors:
            elements = tab.eles(selector)
            for elem in elements:
                if any(char.isdigit() for char in elem.text):
                    logger.success(f"可能的分数元素: {selector} - 文本: {elem.text}")
        
        # 等待一段时间让用户观察
        logger.info("等待30秒以便观察页面...")
        time.sleep(30)
        
        # 关闭浏览器
        browser_controller.close_page()
        logger.success("测试完成")
        
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        return False

if __name__ == "__main__":
    test_passport_elements()
