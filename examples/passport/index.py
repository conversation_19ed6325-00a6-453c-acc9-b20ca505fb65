import click
import time
import random
import os
import sys
from datetime import datetime
from loguru import logger
from retry import retry

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from passport_data_utils import PassportDataUtil
from config import DEFAULT_BROWSER_TYPE
from src.browsers import BROWSER_TYPES, BrowserType
from src.controllers import BrowserController
from src.utils.common import parse_indices
from src.utils.thread_executor import ThreadExecutor
from src.utils.element_util import get_element
from src.browsers.operations import try_click

default_browser_type = DEFAULT_BROWSER_TYPE

logger.add("logs/passport.log", rotation="10MB", level="SUCCESS")


class PassportAutomation:
    """Passport.xyz 自动化认证类"""

    BASE_URL = "https://app.passport.xyz/#/dashboard"

    def __init__(self, browser_type: BrowserType, id: str):
        self.id = id
        self.browser_type = browser_type
        self.browser_controller = BrowserController(browser_type, id)
        self.address = self.browser_controller.browser_config.evm_address

        self.data = PassportDataUtil()
        self._init_data()

    def _init_data(self):
        """初始化数据"""
        try:
            data = {
                "index": self.id,
                "wallet_address": self.address,
                "browser_type": self.browser_type.value,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            }

            # 检查记录是否已存在
            existing_data = self.data.get_by_index(self.id)
            if existing_data:
                # 如果记录已存在，更新基本信息
                self.data.update_by_index(self.id, data)
                logger.info(f"【{self.id}】更新现有记录: {self.address}")
            else:
                # 如果记录不存在，添加新记录
                self.data.add(data)
                logger.info(f"【{self.id}】添加新记录: {self.address}")
        except Exception as e:
            logger.error(f"【{self.id}】{self.address} 初始化数据失败: {e}")

    @retry(tries=3, delay=2)
    def login_okx_wallet(self):
        """登录 OKX 钱包"""
        try:
            logger.info(f"【{self.id}】开始登录 OKX 钱包...")
            self.browser_controller.okx_wallet_login()
            logger.success(f"【{self.id}】OKX 钱包登录成功")
            return True
        except Exception as e:
            logger.error(f"【{self.id}】OKX 钱包登录失败: {e}")
            raise

    @retry(tries=3, delay=2)
    def navigate_to_site(self):
        """访问 Passport.xyz 网站"""
        try:
            logger.info(f"【{self.id}】访问 Passport.xyz 网站...")
            tab = self.page.new_tab(self.BASE_URL)
            time.sleep(3)

            # 检查页面是否加载成功
            title = tab.title
            if "Passport" not in title:
                logger.warning(f"【{self.id}】页面标题可能不正确: {title}")

            logger.success(f"【{self.id}】页面加载成功: {title}")
            return tab
        except Exception as e:
            logger.error(f"【{self.id}】访问网站失败: {e}")
            raise

    @retry(tries=3, delay=2)
    def connect_wallet(self, tab):
        """连接钱包"""
        try:
            logger.info(f"【{self.id}】开始连接钱包...")

            # 检查是否已经连接
            wallet_address_elements = tab.eles("x://*[contains(text(),'0x')]")
            for elem in wallet_address_elements:
                if len(elem.text) > 10 and "0x" in elem.text.lower():
                    logger.success(f"【{self.id}】钱包已连接: {elem.text}")
                    return True

            # 查找连接钱包按钮 - 使用测试中发现的精确选择器
            connect_btn = get_element(tab, "x://*[contains(text(),'Connect Wallet')]", timeout=10)

            if not connect_btn:
                logger.warning(f"【{self.id}】未找到连接钱包按钮，可能已连接")
                return True

            # 点击连接钱包
            logger.info(f"【{self.id}】点击连接钱包按钮...")
            connect_btn.click()
            time.sleep(3)

            # 查找并点击 OKX 钱包选项
            okx_btn = get_element(tab, "x://button[contains(text(),'OKX') or contains(@class,'okx')]", timeout=10)

            if okx_btn:
                logger.info(f"【{self.id}】点击 OKX 钱包选项...")
                okx_btn.click()
                time.sleep(2)

            # 调用钱包连接
            logger.info(f"【{self.id}】调用 OKX 钱包连接...")
            self.browser_controller.okx_wallet_connect()
            time.sleep(5)

            # 验证连接是否成功
            wallet_address_elements = tab.eles("x://*[contains(text(),'0x')]")
            for elem in wallet_address_elements:
                if len(elem.text) > 10 and "0x" in elem.text.lower():
                    logger.success(f"【{self.id}】钱包连接成功: {elem.text}")
                    return True

            raise Exception("钱包连接验证失败")

        except Exception as e:
            logger.error(f"【{self.id}】连接钱包失败: {e}")
            raise

    def verify_gmail(self, tab):
        """Gmail认证"""
        try:
            logger.info(f"【{self.id}】开始Gmail认证...")

            # 查找Gmail/Google认证相关元素
            gmail_selectors = [
                "x://*[contains(text(),'Google')]//button",
                "x://*[contains(text(),'Gmail')]//button",
                "x://button[contains(text(),'Google')]",
                "x://button[contains(text(),'Gmail')]"
            ]

            gmail_btn = None
            for selector in gmail_selectors:
                gmail_btn = get_element(tab, selector, timeout=3)
                if gmail_btn:
                    logger.info(f"【{self.id}】找到Gmail按钮: {selector}")
                    break

            if not gmail_btn:
                logger.warning(f"【{self.id}】未找到Gmail认证按钮")
                return False

            gmail_btn.click()
            time.sleep(5)

            # 处理可能的OAuth流程
            logger.info(f"【{self.id}】等待Gmail认证完成...")
            time.sleep(10)

            logger.success(f"【{self.id}】Gmail认证尝试完成")
            return True

        except Exception as e:
            logger.error(f"【{self.id}】Gmail认证失败: {e}")
            return False

    def verify_discord(self, tab):
        """Discord认证"""
        try:
            logger.info(f"【{self.id}】开始Discord认证...")

            # 查找Discord认证按钮
            discord_selectors = [
                "x://*[contains(text(),'Discord')]//button",
                "x://button[contains(text(),'Discord')]"
            ]

            discord_btn = None
            for selector in discord_selectors:
                discord_btn = get_element(tab, selector, timeout=3)
                if discord_btn:
                    logger.info(f"【{self.id}】找到Discord按钮: {selector}")
                    break

            if not discord_btn:
                logger.warning(f"【{self.id}】未找到Discord认证按钮")
                return False

            discord_btn.click()
            time.sleep(5)

            # 处理Discord OAuth流程
            logger.info(f"【{self.id}】等待Discord认证完成...")
            time.sleep(10)

            logger.success(f"【{self.id}】Discord认证尝试完成")
            return True

        except Exception as e:
            logger.error(f"【{self.id}】Discord认证失败: {e}")
            return False

    def verify_nft(self, tab):
        """NFT认证"""
        try:
            logger.info(f"【{self.id}】开始NFT认证...")

            # 查找NFT认证按钮
            nft_selectors = [
                "x://*[contains(text(),'NFT')]//button",
                "x://button[contains(text(),'NFT')]",
                "x://*[contains(text(),'Ethereum')]//button"
            ]

            nft_btn = None
            for selector in nft_selectors:
                nft_btn = get_element(tab, selector, timeout=3)
                if nft_btn:
                    logger.info(f"【{self.id}】找到NFT按钮: {selector}")
                    break

            if not nft_btn:
                logger.warning(f"【{self.id}】未找到NFT认证按钮")
                return False

            nft_btn.click()
            time.sleep(5)

            # 处理NFT验证流程
            logger.info(f"【{self.id}】等待NFT验证完成...")
            time.sleep(10)

            logger.success(f"【{self.id}】NFT认证尝试完成")
            return True

        except Exception as e:
            logger.error(f"【{self.id}】NFT认证失败: {e}")
            return False

    def get_humanity_score(self, tab):
        """获取Unique Humanity Score"""
        try:
            logger.info(f"【{self.id}】获取Unique Humanity Score...")

            # 等待页面更新
            time.sleep(5)

            # 查找分数显示元素的多种选择器
            score_selectors = [
                "x://*[contains(text(),'Unique Humanity Score')]/following-sibling::*",
                "x://*[contains(@class,'score')]",
                "x://*[contains(@class,'humanity')]",
                "x://*[contains(text(),'Score:')]",
                "x://*[contains(text(),'Score')]//following-sibling::*",
                "x://*[text()[contains(.,'.')]]"  # 查找包含小数点的文本
            ]

            for selector in score_selectors:
                elements = tab.eles(selector)
                for elem in elements:
                    text = elem.text.strip()
                    # 检查是否包含数字和小数点，可能是分数
                    if text and any(char.isdigit() for char in text) and '.' in text:
                        # 尝试提取数字
                        import re
                        numbers = re.findall(r'\d+\.?\d*', text)
                        if numbers:
                            score = numbers[0]
                            logger.success(f"【{self.id}】获取到分数: {score} (来源: {text})")
                            return score

            # 如果没有找到具体分数，查找任何包含数字的元素
            all_elements = tab.eles("x://*[text()]")
            for elem in all_elements:
                text = elem.text.strip()
                if text and len(text) < 20:  # 短文本更可能是分数
                    import re
                    if re.match(r'^\d+\.?\d*$', text):  # 纯数字
                        logger.info(f"【{self.id}】可能的分数: {text}")
                        return text

            logger.warning(f"【{self.id}】未找到分数显示元素")
            return "0"

        except Exception as e:
            logger.error(f"【{self.id}】获取分数失败: {e}")
            return "0"

    def _log_results(self, success, gmail_verified=False, discord_verified=False, nft_verified=False, humanity_score="0", error_message=None):
        """记录结果到CSV"""
        try:
            csv_data = {
                "gmail_verified": "1" if gmail_verified else "0",
                "discord_verified": "1" if discord_verified else "0",
                "nft_verified": "1" if nft_verified else "0",
                "humanity_score": humanity_score,
                "status": "success" if success else "failed",
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "error_message": error_message or "",
            }

            self.data.update_by_index(self.id, csv_data)
            logger.info(f"【{self.id}】结果已记录到CSV")

        except Exception as e:
            logger.error(f"【{self.id}】记录结果到CSV失败: {e}")

    @retry(tries=2, delay=5)
    def _task(self):
        """执行自动化认证任务"""
        tab = None
        self.page = self.browser_controller.page
        try:
            logger.info(f"【{self.id}】开始执行自动化认证任务")

            # 1. 登录 OKX 钱包
            self.login_okx_wallet()

            # 2. 访问网站
            tab = self.navigate_to_site()

            # 3. 连接钱包
            self.connect_wallet(tab)

            # 4. 执行各项认证
            gmail_verified = self.verify_gmail(tab)
            discord_verified = self.verify_discord(tab)
            nft_verified = self.verify_nft(tab)

            # 5. 获取分数
            humanity_score = self.get_humanity_score(tab)

            # 6. 记录结果
            success = gmail_verified or discord_verified or nft_verified
            self._log_results(success, gmail_verified, discord_verified, nft_verified, humanity_score)

            if not success:
                raise Exception("所有认证都失败")

            logger.success(f"【{self.id}】自动化认证任务完成，分数: {humanity_score}")
            return True

        except Exception as e:
            logger.error(f"【{self.id}】执行自动化认证任务异常: {e}")
            self._log_results(False, error_message=str(e))
            raise
        finally:
            # 记录任务完成，浏览器将在外层统一关闭
            if tab:
                logger.info(f"【{self.id}】任务执行完成")

    def task(self):
        """任务入口"""
        try:
            # 检查是否已有记录
            existing_data = self.data.get_by_index(self.id)
            if existing_data and existing_data.get("status") == "success":
                logger.success(f"【{self.id}】自动化认证已完成")
                return True

            return self._task()
        except Exception as e:
            logger.error(f"【{self.id}】任务执行失败: {e}")
            return False


@click.group()
def cli():
    pass


@cli.command("run")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--indices", type=str, prompt="请输入浏览器序号", help="浏览器序号，支持范围和逗号分隔")
@click.option("-c", "--concurrency", type=int, default=1, help="并发数")
def run(type, indices, concurrency):
    """运行自动化认证任务"""
    try:
        indices_list = parse_indices(indices)
        random.shuffle(indices_list)
        logger.info(f"开始执行自动化认证任务，浏览器序号: {indices_list}")

        failed_indices = []

        def create_task(index):
            automation = PassportAutomation(type, str(index))
            try:
                result = automation.task()
                if not result:
                    failed_indices.append(str(index))
                return {"index": index, "success": result, "error": None}
            except Exception as e:
                logger.error(f"【{index}】任务执行异常: {e}")
                return {"index": index, "success": False, "error": str(e)}
            finally:
                # 关闭浏览器窗口
                try:
                    automation.browser_controller.close_page()
                    logger.info(f"【{index}】浏览器窗口已关闭")
                except Exception as e:
                    logger.warning(f"【{index}】关闭浏览器窗口失败: {e}")

        executor = ThreadExecutor(
            workers=concurrency,
            timeout=1800,  # 30分钟超时
            retries=2,
            interval=5,
            task_name=f"Passport-{type}-{datetime.now().strftime('%Y%m%d%H%M%S')}",
            raise_exception=False,
        )

        executor.run_batch(create_task, indices_list)

        # 输出失败统计
        logger.info(f"failed_counts: {','.join(failed_indices)}")

    except Exception as e:
        logger.error(f"执行任务时出错: {e}")


if __name__ == "__main__":
    cli()

    # 测试代码
    # index = 1
    # automation = PassportAutomation(BrowserType.CHROME, str(index))
    # automation.task()
