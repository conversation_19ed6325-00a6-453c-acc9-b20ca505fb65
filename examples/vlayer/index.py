import click
import random
from loguru import logger
from src.browsers import BROWSER_TYPES, BrowserType
from config import DEFAULT_BROWSER_TYPE
from src.controllers import BrowserController
from src.utils.common import parse_indices, get_project_root_path
from time import sleep
from retry import retry
from src.browsers.operations import try_click
from src.utils.thread_executor import ThreadExecutor
from faker import Faker

faker = Faker()
default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]


class Vlayer:
    def __init__(self, browser_type: BrowserType, id: str):
        self.browser_type = browser_type
        self.id = id
        self.browser_controller = BrowserController(browser_type, id)
        self.page = self.browser_controller.page

    def get_email(self):
        index = int(self.id) - 1
        root_path = get_project_root_path()
        with open(f"{root_path}/examples/vlayer/emails.txt", "r") as f:
            return f.readlines()[index].strip()

    def task_register(self):
        lasted_tab = self.page.new_tab("https://badges.vlayer.xyz/join")

        for i in range(2):
            try:
                iframe = lasted_tab.ele("x://iframe[contains(@src,'typeform')]")

                username_input = iframe.ele("x://input")
                if len(username_input.value) == 0:
                    username_input.input(faker.name())
                    sleep(1)

                iframe.ele(
                    "x://button[@data-qa='ok-button-visible deep-purple-ok-button-visible']",
                    timeout=10,
                ).click()

                sleep(3)

                email_input = iframe.ele("x://input[@type='email']")
                email_input.clear(True)
                email = self.get_email()
                email_input.input(email)

                sleep(1)

                iframe.ele(
                    "x://button[@data-qa='submit-button deep-purple-submit-button']",
                    timeout=10,
                ).click()

                if iframe.wait.ele_deleted("x://input[@type='email']"):
                    logger.success(f"【{self.id}】 任务完成")
                    sleep(3)
                    self.page.quit()
                    return True

                lasted_tab.refresh()
                sleep(3)
                continue

            except Exception as e:
                logger.error(f"【{self.id}】 任务失败: {e}")
                lasted_tab.refresh()
                sleep(3)
                continue

        logger.error(f"【{self.id}】 任务失败")
        return False


def _register_task(type, index):
    try:
        browser = Vlayer(type, str(index))
        return browser.task_register()
    except Exception as e:
        logger.error(f"{index} 执行任务异常 {e}")
        return False


@click.group()
def cli():
    pass


@cli.command("r")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=5, help="并发数量")
def register(type, index, workers):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)

        def process_task(index):
            return _register_task(type, index)

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=6 * 3600,  # 6小时超时
            retries=1,
            interval=10,
            task_name=f"Vlayer-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        results = executor.run_batch(process_task, indices)
        logger.info(f"任务执行结果: {results}")
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


# 注册
# python3 examples/vlayer/giveaway2.py r -t ads -i 1-10 -w 5

if __name__ == "__main__":
    cli()
    # _register_task(BrowserType.BIT, "11")
