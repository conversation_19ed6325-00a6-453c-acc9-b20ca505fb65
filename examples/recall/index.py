import os
import random
from time import sleep

import click
from loguru import logger
from retry import retry

from config import DEFAULT_BROWSER_TYPE
from src.browsers import BROWSER_TYPES, BrowserType
from src.browsers.operations import try_click
from src.controllers import BrowserController
from src.utils.common import get_project_root_path, parse_indices
from src.utils.data_utils import DataUtil
from src.utils.element_util import get_element
from src.utils.thread_executor import ThreadExecutor

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]

logger.add("logs/recall.log", rotation="10MB", level="SUCCESS")


class Recall:
    def __init__(self, browser_type: BrowserType, id: str, data: DataUtil):
        self.id = id
        self.browser_type = browser_type
        self.browser_controller = BrowserController(browser_type, id)
        self.page = self.browser_controller.page
        self.data = data
        self.address = self.browser_controller.browser_config.evm_address
        self._init_data()

    def _init_data(self):
        try:
            data = self.data.get(self.address)
            if data:
                return

            data = {
                "index": self.id,
                "type": self.browser_type.value,
                "address": self.address,
            }
            self.data.add(data)
        except Exception as e:
            logger.error(f"【{self.id}】{self.address} 初始化数据失败: {e}")

    def _get_referral_codes(self):
        # 从文件referral_code.txt中读取referral_code
        referral_code_file = get_project_root_path() + "/examples/recall/referral_code.txt"
        with open(referral_code_file) as f:
            referral_code = f.read().splitlines()

        return referral_code

    def _sign_message(self, lasted_tab):
        # 点击签名
        try:
            # sign_view = lasted_tab.ele("tag:w3m-modal").sr('tag:wui-flex').ele('tag:w3m-router').sr('tag:w3m-siwx-sign-message-view').shadow_root
            # sign_view.child('tag:wui-flex', 4).child('tag:wui-button', 2).click(True)
            js_str = """
                document.querySelector("body > w3m-modal").shadowRoot.querySelector("wui-flex > wui-card > w3m-router").shadowRoot.querySelector("div > w3m-siwx-sign-message-view").shadowRoot.querySelector("wui-flex:nth-child(4) > wui-button:nth-child(2)").click()
            """
            lasted_tab.run_js(js_str)
            sleep(1)
            self.browser_controller.okx_wallet_connect()
            sleep(3)

            modal = lasted_tab.ele("tag:w3m-modal", timeout=3)
            if not modal:
                return True

            flex = modal.sr("tag:wui-flex", timeout=3)
            if not flex:
                return True

            router = flex.ele("tag:w3m-router", timeout=3)
            if not router:
                return True

            sign_view = router.sr("tag:w3m-siwx-sign-message-view", timeout=3)
            if not sign_view:
                return True

            return False
        except Exception:
            pass

    def _choose_okx_wallet(self, lasted_tab):
        # 选okx钱包
        try:
            # js = """
            #     document.querySelector("body > w3m-modal").shadowRoot.querySelector("wui-flex > wui-card > w3m-router").shadowRoot.querySelector("div > w3m-connect-view").shadowRoot.querySelector("wui-flex > wui-flex.connect > wui-flex > w3m-wallet-login-list").shadowRoot.querySelector("wui-flex > w3m-connector-list").shadowRoot.querySelector("wui-flex > w3m-connect-injected-widget").shadowRoot.querySelector("wui-flex > wui-list-wallet:nth-child(1)").shadowRoot.querySelector("button > wui-text").click()
            # """
            okx_wallet_btn = (
                lasted_tab("t:w3m-modal")
                .sr("t:wui-flex")
                .ele("t:wui-card")
                .ele("t:w3m-router")
                .sr("t:div")
                .ele("t:w3m-connect-view")
                .sr("t:wui-flex")
                .ele("t:w3m-wallet-login-list")
                .sr("t:wui-flex")
                .ele("t:w3m-connector-list")
                .sr("t:wui-flex")
                .ele("t:w3m-connect-injected-widget")
                .sr("t:wui-flex")
                .ele("@name=OKX Wallet")
            )
            if okx_wallet_btn:
                okx_wallet_btn.click()
                self.browser_controller.okx_wallet_connect()
                sleep(2)
                return True
            else:
                return False
        except Exception:
            return False

    def _handler_sign_dialog(self, lasted_tab):
        # 检查签名对话框是否存在
        try:
            modal = lasted_tab.ele("tag:w3m-modal", timeout=3)
            if not modal:
                return True

            flex = modal.sr("tag:wui-flex", timeout=3)
            if not flex:
                return True

            router = flex.ele("tag:w3m-router", timeout=3)
            if not router:
                return True

            sign_view = router.sr("tag:w3m-siwx-sign-message-view", timeout=3)
            if not sign_view:
                return True

            # 取消签名
            js_str = """
                document.querySelector("body > w3m-modal").shadowRoot.querySelector("wui-flex > wui-card > w3m-router").shadowRoot.querySelector("div > w3m-siwx-sign-message-view").shadowRoot.querySelector("wui-flex:nth-child(4) > wui-button:nth-child(1)").click() 
            """
            lasted_tab.run_js(js_str)
            sleep(1)

            # 关闭签名对话框
            js_str = """
                document.querySelector("body > w3m-modal").shadowRoot.querySelector("wui-flex > wui-card > w3m-header").shadowRoot.querySelector("wui-flex > wui-icon-link:nth-child(3)").shadowRoot.querySelector("button").click()   
            """
            lasted_tab.run_js(js_str)
            sleep(1)

            modal = lasted_tab.ele("tag:w3m-modal", timeout=3)
            if not modal:
                return True

            flex = modal.sr("tag:wui-flex", timeout=3)
            if not flex:
                return True

            return False
        except Exception as e:
            logger.error(f"【{self.id}】 签名对话框检查失败: {e}")
            return False

    def _connect_wallet(self, lasted_tab):
        try_count = 3
        wallet_elet = lasted_tab.ele("x://button[contains(.,'0x')]", timeout=10)
        if wallet_elet:
            logger.success(f"【{self.id}】 钱包已连接")
            self._sign_message(lasted_tab)
            return True

        self._handler_sign_dialog(lasted_tab)

        for i in range(try_count):
            logger.info(f"【{self.id}】 第{i + 1}次尝试连接钱包")
            # 点击连接钱包按钮
            result = try_click(lasted_tab, xpath="x://button[.='Connect Wallet']", id=self.id)
            if not result:
                logger.warning("点击钱包按钮异常...")
                continue
            sleep(2)

            # 选okx钱包
            logger.info(f"【{self.id}】 选择okx钱包")
            result = self._choose_okx_wallet(lasted_tab)
            if not result:
                continue

            logger.info(f"【{self.id}】 签名okx钱包")
            self._sign_message(lasted_tab)
            wallet_elet = lasted_tab.ele("x://button[contains(.,'0x')]", timeout=20)
            if wallet_elet:
                logger.success(f"【{self.id}】 钱包已连接")
                return True

            lasted_tab.refresh()
            sleep(3)

        logger.error(f"【{self.id}】 连接钱包失败，已尝试{try_count}次")
        return False

    def _task_invite(self, lasted_tab):
        try:
            data = self.data.get(self.address)
            if not data:
                logger.error(f"【{self.id}】 数据不存在")
                return False

            referral_code = data.get("referral_code")
            if referral_code:
                logger.success(f"【{self.id}】 已经填写过邀请码")
                return True

            referral_codes = self._get_referral_codes()
            # 随机获取一个referral_code
            referral_code = random.choice(referral_codes)
            # 输入referral_code
            lasted_tab.ele("x://input[@placeholder='Enter referral code']").input(referral_code)
            sleep(1)

            ele = lasted_tab("x://h6[@class='px-8 font-bold']", timeout=5)
            if ele:
                owner_referral_code = ele.text
                self.data.update(self.address, {"owner_referral_code": owner_referral_code})

            # 点击开始
            lasted_tab.listen.start("https://gql3.absinthe.network/v1/graphql")
            lasted_tab.ele("x://button[.='Apply Code']").click()

            res = lasted_tab.listen.wait(timeout=30)
            if not res:
                logger.error(f"【{self.id}】 填写邀请码失败")
                return False

            response_data = res.response.body
            data = response_data.get("data", {})
            if not data:
                errors = response_data.get("errors", [])
                if len(errors) > 0:
                    logger.warning(f"【{self.id}】 用户可能已经填过邀请码...")
                    self.data.update(self.address, {"referral_code": referral_code})
                    return True

            is_success = data.get("apply_referral_code").get("success")
            if is_success:
                self.data.update(self.address, {"referral_code": referral_code})
                logger.success(f"【{self.id}】 邀请码填写成功")
                return True

            return False

        except Exception as e:
            logger.error(f"【{self.id}】 填写邀请码失败: {e}")
            return False

    def _get_bind_button_with_text(self, lasted_tab, text):
        return lasted_tab.ele(f"x://span[.='{text}']/../..//button[.='Connect']")

    def _check_linked(self, lasted_tab, text):
        return lasted_tab.ele(f"x://span[.='{text}']/../..//button[.='Disconnect']", timeout=5)

    def _task_bind_x(self, lasted_tab):
        try:
            data = self.data.get(self.address)
            if not data:
                logger.error(f"【{self.id}】 数据不存在")
                return False

            linked_twitter = data.get("linked_twitter")
            if linked_twitter:
                logger.success(f"【{self.id}】 Twitter已绑定")
                return True

            ele = self._check_linked(lasted_tab, "Twitter")
            if ele:
                logger.success(f"【{self.id}】{self.address}Twitter绑定成功")
                self.data.update(self.address, {"linked_twitter": 1})
                return True

            bind_button = self._get_bind_button_with_text(lasted_tab, "Twitter")
            if not bind_button:
                logger.error(f"【{self.id}】 未找到Twitter绑定按钮")
                return False

            bind_button.click()
            sleep(1)

            tab = lasted_tab.wait.url_change(text="i/flow/login", timeout=10)
            if tab:
                self.browser_controller.login_x_with_auth()

            lasted_tab.wait.url_change(text="i/oauth2/authorize", timeout=10)
            lasted_tab.wait.ele_displayed("x://button[@data-testid='OAuth_Consent_Button']", timeout=10)
            allow_btn = get_element(lasted_tab, "x://button[@data-testid='OAuth_Consent_Button']", 5)
            if not allow_btn:
                logger.error(f"【{self.id}】{self.address}未找到Twitter授权按钮")
                return False

            allow_btn.click()
            sleep(3)

            lasted_tab = lasted_tab.wait.url_change(text="recall/account", timeout=20)
            if not lasted_tab:
                logger.error(f"【{self.id}】{self.address} Twitter绑定失败")
                return False

            self._handler_sign_dialog(lasted_tab)

            ele = self._check_linked(lasted_tab, "Twitter")
            if ele:
                logger.success(f"【{self.id}】{self.address}Twitter绑定成功")
                self.data.update(self.address, {"linked_twitter": 1})
                return True
            else:
                logger.error(f"【{self.id}】{self.address} Twitter绑定失败")
                return False

        except Exception as e:
            logger.error(f"【{self.id}】 点击开始失败: {e}")
            return False

    def _task_ask(self, lasted_tab):
        try:
            self._task_invite(lasted_tab)
            self._task_bind_x(lasted_tab)

        except Exception as e:
            logger.error(f"【{self.id}】 点击开始失败: {e}")
            return False

    @retry(tries=3, delay=1)
    def _task(self):
        lasted_tab = self.page.new_tab("https://boost.absinthe.network/recall/account")
        sleep(3)

        # 3. 连接钱包
        result = self._connect_wallet(lasted_tab)
        if not result:
            logger.error(f"【{self.id}】 连接钱包失败")
            raise Exception("连接钱包失败")

        # 4. 点击开始
        self._task_ask(lasted_tab)

        return True

    def task(self):
        try:
            data = self.data.get(self.address)
            if not data:
                logger.error(f"【{self.id}】 数据不存在")
                return False

            linked_twitter = data.get("linked_twitter")
            referral_code = data.get("referral_code")
            if linked_twitter and referral_code:
                logger.success(f"【{self.id}】 任务已经完成, 无需重复完成...")
                return True

            self.browser_controller.window_max()

            # 1. 初始化浏览器
            self.browser_controller.okx_wallet_login()
            return self._task()

        except Exception as e:
            if "连接钱包失败" in str(e):
                raise e

            logger.error(f"【{self.id}】 任务执行失败: {e}")
            return False
        finally:
            try:
                self.page.quit()
            except Exception as e:
                logger.error(f"【{self.id}】 关闭页面失败: {e}")


def _get_data_util() -> DataUtil:
    """
    获取数据工具实例

    Returns:
        DataUtil: 数据工具实例
    """
    data_dir = os.path.join(get_project_root_path(), "examples", "recall")
    csv_path = os.path.join(data_dir, "recall.csv")
    return DataUtil(csv_path)


def _run_task(type, index):
    try:
        browser = Recall(type, str(index), _get_data_util())
        browser.task()
    except Exception as e:
        logger.error(f"{index} 执行任务异常 {e}")


@click.group()
def cli():
    pass


@cli.command("run")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
def run(type, index, workers):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)
        data = _get_data_util()

        def process_task(index):
            try:
                browser = Recall(type, str(index), data)
                return browser.task()
            except Exception as e:
                logger.error(f"账号 {index} 执行任务异常: {e}")
                return False

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=6 * 3600,  # 6小时超时
            retries=3,
            interval=10,
            task_name=f"Recall-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        results = executor.run_batch(process_task, indices)
        logger.info(f"任务执行结果: {results}")
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


# 点击
# python3 examples/recall/giveaway2.py run -t ads -i 1-10


if __name__ == "__main__":
    cli()
    # _run_task(BrowserType.ADS, "82")
