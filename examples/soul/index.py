import json
import os
import random
import re
from time import sleep

import click
from loguru import logger

from config import DEFAULT_BROWSER_TYPE
from src.browsers import BROWSER_TYPES
from src.controllers import BrowserController
from src.enums.browsers_enums import BrowserType
from src.utils.common import get_project_root_path, parse_indices
from src.utils.data_utils import DataUtil
from src.utils.element_util import get_element, get_elements
from src.utils.thread_executor import ThreadExecutor

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]
logger.add("logs/soul.log", rotation="10MB", level="SUCCESS")

PROXY_URL = os.environ.get("PROXY_URL")


class SoulProtocol:
    def __init__(self, browser_type: BrowserType, index: str, data_util: DataUtil):
        self.id = index
        self.browser_type = browser_type
        self.browser_controller = BrowserController(browser_type, index)
        self.address = self.browser_controller.browser_config.evm_address
        self.data_util = data_util
        self._init_data()

    def _init_data(self):
        try:
            data = self.data_util.get(self.address)
            if data:
                return

            data = {
                "index": self.id,
                "type": self.browser_type.value,
                "address": self.address,
            }
            self.data_util.add(data)
        except Exception as e:
            logger.error(f"【{self.id}】初始化数据失败: {e}")

    def is_referral_by(self) -> bool:
        data = self.data_util.get(self.address)
        referral_by_code = data.get("referral_by_code", "")
        return len(referral_by_code) > 0

    def get_referral_by_code(self) -> str:
        try:
            path = os.path.join(os.path.dirname(__file__), "referral_codes.txt")
            with open(path) as f:
                referral_codes = [line.strip() for line in f.readlines()]
            return random.choice(referral_codes) if referral_codes else ""
        except Exception as e:
            logger.error(f"【{self.id}】 获取邀请码失败: {str(e)}")
            return ""

    def is_login(self, tab):
        logger.info(f"【{self.id}】检查是否登录")
        xpath = "x:(//button[.//span[contains(@class, 'ScrambleText_hidden') and contains(text(), '0x')]])[1]"
        wallet_btn = get_element(tab, xpath, 5)
        if wallet_btn:
            logger.info(f"【{self.id}】已登录")
            return True
        logger.info(f"【{self.id}】未登录")
        return False

    def _setup_browser(self) -> bool:
        """
        设置浏览器环境

        Returns:
            bool: 设置是否成功
        """
        try:
            self.browser_controller.window_max()
            self.browser_controller.okx_wallet_login()
            logger.debug(f"【{self.id}】浏览器环境设置完成")
            return True
        except Exception as e:
            logger.error(f"【{self.id}】浏览器环境设置失败: {e}")
            return False

    def login(self):
        referral_code = ""
        login_url = "https://app.soul.io/seeds/quests"
        record = self.data_util.get(self.address)
        if record.get("registered", "") != "1":
            referral_code = self.get_referral_by_code()

        if referral_code:
            login_url = f"https://app.soul.io/?referredBy={referral_code}"
            logger.info(f"【{self.id}】使用邀请码 {referral_code} 注册")

        for i in range(3):
            tab = None
            try:
                tab = self.browser_controller.page.new_tab(login_url)
                logger.debug(f"【{self.id}】等待页面loading...")
                tab.wait.ele_displayed("x://*[@class='is-loaded']", timeout=20)
                sleep(5)
                if self.is_login(tab):
                    if record.get("registered", "") != "1":
                        self.data_util.update(self.address, {"registered": "1", "referral_by_code": referral_code})
                        tab.get("https://app.soul.io/seeds/quests")
                        tab.wait.ele_displayed("x://*[@class='is-loaded']", timeout=20)
                        sleep(5)
                    return True
                connect_btn = get_element(tab, "x:(//span[text()='Connect Wallet']/ancestor::button)[1]", 5)
                if connect_btn and connect_btn.states.has_rect:
                    connect_btn.click()
                    logger.debug(f"【{self.id}】点击连接钱包按钮")
                    sleep(2)

                okx_wallet_btn = get_element(tab, "x://button[@data-testid='rk-wallet-option-com.okex.wallet']", 5)
                if okx_wallet_btn and connect_btn.states.has_rect:
                    okx_wallet_btn.click()
                    logger.debug(f"【{self.id}】点击OKX钱包")
                    sleep(1)

                if self.browser_controller.page.wait.new_tab(timeout=10, curr_tab=tab):
                    if not self.browser_controller.okx_wallet_connect():
                        logger.warning(f"【{self.id}】第 {i+1} 次登录，连接钱包失败, retry...")
                        continue
                    logger.info(f"【{self.id}】连接钱包成功")
                    sleep(2)

                sign_btn = get_element(tab, "x://div[@id='modal-root']//section//button[.//span[text()='SIGN']]", 5)
                if sign_btn and sign_btn.states.has_rect:
                    sign_btn.click()
                    if self.browser_controller.okx_wallet_sign():
                        logger.info(f"【{self.id}】签名成功")
                    else:
                        logger.warning(f"【{self.id}】第 {i+1} 次登录, 签名失败, retry...")
                        continue

                if self.is_login(tab):
                    if record.get("registered", "") != "1":
                        self.data_util.update(self.address, {"registered": "1", "referral_by_code": referral_code})
                        tab.get("https://app.soul.io/seeds/quests")
                        tab.wait.ele_displayed("x://*[@class='is-loaded']", timeout=20)
                        sleep(5)
                    return True
                logger.warning(f"【{self.id}】第 {i+1} 次登陆失败, retry...")
            except Exception as e:
                logger.warning(f"【{self.id}】第 {i+1} 次登陆失败, error={str(e)}")
            tab.close()
        logger.error(f"【{self.id}】尝试登录3次后仍失败")
        return False

    def get_referral_code(self) -> bool:
        try:
            if not self.browser_controller.browser_config.tg_cellphone:
                logger.info(f"【{self.id}】未配置Telegram, 跳过...")
                return True

            # 1、登录OKX钱包
            if not self._setup_browser():
                return False

            # 2、登录Soul
            if not self.login():
                logger.error(f"【{self.id}】登录失败")
                return False

            lasted_tab = self.browser_controller.page.latest_tab
            lasted_tab.listen.start("https://app.soul.io/seeds/referral", method="POST")
            lasted_tab.get("https://app.soul.io/seeds/referral")
            res = lasted_tab.listen.wait(timeout=90)
            if not res:
                logger.error(f"【{self.id}】获取邀请码失败, 未收到网络响应")
                return False

            data = self._parse_response(res.response.body)
            code = data.get("code", "")
            logger.success(f"【{self.id}】获取邀请码成功: {code}")
            self.data_util.update(self.address, {"referral_code": code})
            return True
        except Exception as e:
            logger.error(f"【{self.id}】获取邀请码失败: {str(e)}")
            return False

    def parse_quests(self):
        lasted_tab = self.browser_controller.page.latest_tab
        lasted_tab.listen.start(targets="https://app.soul.io/seeds/quests", method="POST")
        lasted_tab.get("https://app.soul.io/seeds/quests")
        for packet in lasted_tab.listen.steps():
            req = packet.request.postData
            res = packet.response.body
            if not isinstance(req, list) or self.address not in req:
                continue
            if res is None:
                continue
            if "questsWithStatus" in res:
                continue
            logger.debug(f"【{self.id}】请求数据: {req}, 响应数据: {res}")
            return self._parse_response(res)
        return {}

    def _parse_response(self, text):
        try:
            # 使用正则表达式提取以 "1:" 开头的部分
            match = re.search(r"1:(\{.*})", text, re.DOTALL)
            if not match:
                raise ValueError("未找到以 '1:' 开头的 JSON 对象")

            # 获取匹配到的 JSON 字符串
            json_str = match.group(1)

            # 解析 JSON 字符串为 Python 字典
            target_data = json.loads(json_str)

            # 验证解析结果是否为字典
            if not isinstance(target_data, dict):
                logger.error(f"【{self.id}】字符串不是有效的JSON对象：{text}")
                return {}

            return target_data
        except Exception as e:
            logger.error(f"【{self.id}】解析结果出错，, error={str(e)}")
            return {}

    def rank(self):
        try:
            logger.info(f"【{self.id}】开始更新积分排名")
            latest_tab = self.browser_controller.page.latest_tab
            latest_tab.get("https://app.soul.io/seeds/quests")
            latest_tab.wait.ele_displayed("x://*[@class='is-loaded']", timeout=20)
            sleep(3)
            seeds_span = get_element(latest_tab, "x://span[text()='My Seeds']/following-sibling::div[1]/span", 5)
            seeds = seeds_span.text if seeds_span else ""

            rank_span = get_element(latest_tab, "x://span[text()='My Rank']/following-sibling::div[1]/span", 5)
            rank = rank_span.text.replace("#", "") if rank_span else ""

            self.data_util.update(self.address, {"seeds": seeds, "rank": rank})
            logger.success(f"【{self.id}】当前共 {seeds} 积分, 排行第 {rank} 名")
            return True
        except Exception as e:
            logger.error(f"【{self.id}】更新seeds和rank失败, error={str(e)}")
            return False

    def _x_connect(self):
        try:
            tab = self.browser_controller.page.latest_tab
            sleep(2)
            if "login" in tab.url:
                logger.info(f"【{self.id}】需要登录Twitter")
                if not self.browser_controller.login_x(False):
                    logger.error(f"【{self.id}】Twitter登录失败")
                    return False

            if not tab.wait.url_change(text="x.com/i/oauth2/authorize", timeout=10):
                logger.error(f"【{self.id}】登录后未跳转到授权页面")
                return False

            tab.set.window.max()

            tab.wait.ele_displayed("x://button[@data-testid='OAuth_Consent_Button']", timeout=10)
            oauth_btn = get_element(tab, "x://button[@data-testid='OAuth_Consent_Button']", 5)
            if not oauth_btn:
                logger.error(f"【{self.id}】{self.address}未找到Twitter授权按钮")
                return False

            oauth_btn.click()
            if not tab.wait.url_change("https://app.soul.io/seeds/quests", timeout=30):
                logger.error(f"【{self.id}】绑定Twitter失败")
                return False
            logger.success(f"【{self.id}】绑定Twitter成功")
            tab.wait.ele_displayed("x://*[@class='is-loaded']", timeout=20)

            # 绑定x以后页面刷新了，需要重新点击按钮
            quset_name = "Quest 1: Become Part Of The Soul Protocol Community"
            quest_xpath = f'x://div[text()="{quset_name}"]/ancestor::div[3]/child::div[5]//button'
            quest_btn = get_element(tab, quest_xpath, 10)
            quest_btn.click()

            return True
        except Exception as e:
            logger.error(f"【{self.id}】绑定推特异常, error={str(e)}")
            return False

    def _discord_connect(self):
        try:
            tab = self.browser_controller.page.latest_tab
            sleep(5)
            if "login" in tab.url:
                logger.info(f"【{self.id}】需要登录Discord")
                if not self.browser_controller.login_discord():
                    logger.error(f"【{self.id}】Discord登录失败")
                    return False

            if not tab.wait.url_change(text="discord.com/oauth2/authorize", timeout=10):
                logger.error(f"【{self.id}】登录后未跳转到授权页面")
                return False

            tab.set.window.max()
            tab.wait.ele_displayed("x:(//button)[2]", timeout=5)
            oauth_btn = get_element(tab, "x:(//button)[2]", 5)
            if not oauth_btn:
                logger.error(f"【{self.id}】未找到Discord授权按钮")
                return False

            oauth_btn.click()
            if not tab.wait.url_change("https://app.soul.io/seeds/quests", timeout=30):
                logger.error(f"【{self.id}】绑定Discord失败")
                return False
            logger.success(f"【{self.id}】绑定Discord成功")
            tab.wait.ele_displayed("x://*[@class='is-loaded']", timeout=20)

            # 绑定dc以后页面刷新了，需要重新点击按钮
            quset_name = "Quest 1: Become Part Of The Soul Protocol Community"
            quest_xpath = f'x://div[text()="{quset_name}"]/ancestor::div[3]/child::div[5]//button'
            quest_btn = get_element(tab, quest_xpath, 10)
            quest_btn.click()

            return True
        except Exception as e:
            logger.error(f"【{self.id}】绑定Discord异常，error={str(e)}")
            return False

    def _discord_join(self):
        sleep(2)
        latest_tab = self.browser_controller.page.latest_tab
        if "discord.com" in latest_tab.url:
            latest_tab.close()
        logger.success(f"【{self.id}】discord加群成功")
        return True

    def _telegram_connect(self):
        try:
            if not self.browser_controller.browser_config.tg_cellphone:
                logger.info(f"【{self.id}】未配置Telegram, 跳过...")
                return False
            logger.debug(f"【{self.id}】开始绑定Telegram")
            latest_tab = self.browser_controller.page.latest_tab
            verify_code_btn = get_element(latest_tab, "x://span[text()='Verify code']/parent::button", 5)
            if verify_code_btn and verify_code_btn.states.has_rect:
                verify_code_btn.click()
                sleep(2)

            verify_code_i = get_element(
                latest_tab,
                "x://a[@href='https://t.me/soul_verification_bot']/parent::div/following-sibling::div/div/input",
                5,
            )
            if not verify_code_i:
                logger.error(f"【{self.id}】未找到Telegram验证码")
                return False
            verify_code = verify_code_i.value
            logger.success(f"【{self.id}】telegram验证码：{verify_code}")

            tg_tab = self.browser_controller.page.new_tab("https://web.telegram.org/k/")
            if tg_tab("Log in to Telegram by QR Code"):
                logger.info(f"【{self.id}】telegram未登录")
                return False

            result = self._telegram_verify(verify_code)
            tab = self.browser_controller.page.get_tab(url="https://app.soul.io/seeds/quests")
            self.browser_controller.page.close_tabs(tab, others=True)
            return result
        except Exception as e:
            logger.error(f"【{self.id}】绑定Telegram异常, error={str(e)}")
            return False

    def _telegram_verify(self, code):
        msg = f"/verify {code}"
        tg_link_k = "https://web.telegram.org/k/#?tgaddr=tg%3A%2F%2Fresolve%3Fdomain%3Dsoul_verification_bot"
        tg_link_a = "https://web.telegram.org/a/#?tgaddr=tg%3A%2F%2Fresolve%3Fdomain%3Dsoul_verification_bot"
        tab = self.browser_controller.page.new_tab(tg_link_k)

        if tab.wait.url_change(text="https://web.telegram.org/k/#@soul_verification_bot", timeout=30):
            # 先打开https://web.telegram.org/k/
            start_btn_xpath = (
                "x://button[@class='btn-primary btn-transparent text-bold chat-input-control-button rp' "
                "and ./div[@class='c-ripple'] and ./span[@class='i18n']]"
            )
            start_btn = get_element(tab, start_btn_xpath, 5)
            if not start_btn:
                logger.error(f"【{self.id}】未找到Start按钮")
                return False
            start_btn.click()
            sleep(1)
            input_message_xpath = (
                "x://div[contains(@class, 'input-message-input') and contains(@class, 'scrollable') "
                "and contains(@class, 'scrollable-y') and contains(@class, 'is-empty')]"
            )
            tab.actions.move_to(input_message_xpath).click().type(msg).key_down("Enter")
            sleep(2)

            join_a = get_element(tab, "x://a[contains(@href, 'https://t.me')]", 5)
            if join_a:
                join_a.click()
                sleep(2)

            join_group_btn = get_element(tab, "x:(//button[@class='popup-button btn primary rp'])[1]", 5)
            if join_group_btn:
                join_group_btn.click()

            if tab.wait.url_change(text="https://web.telegram.org/k/#-2018147166", timeout=30):
                get_element(tab, "x://button[@class='reply-markup-button rp']", 5).click()
                logger.success(f"【{self.id}】tg soul_verification_bot群组加入成功")
            else:
                logger.warning(
                    f"【{self.id}】tg soul_verification_bot群组加入失败, "
                    "请手动加群：https://web.telegram.org/k/#@soul_verification_bot"
                )
        else:
            # 后打开# 先打开https://web.telegram.org/a/
            tab = self.browser_controller.page.new_tab(tg_link_a)
            if not tab.wait.url_change(text="https://web.telegram.org/a/#8075155017", timeout=30):
                logger.error(f"【{self.id}】未找到tg soul_verification_bot群组")
                return False
            start_btn_xpath = (
                "//button[@class='Button tiny primary fluid has-ripple' and ./div[@class='ripple-container']]"
            )
            start_btn = get_element(tab, start_btn_xpath, 5)
            if not start_btn:
                logger.error(f"【{self.id}】未找到Start按钮")
                return False
            start_btn.click()
            tab.actions.move_to("#editable-message-text").click().type(msg).key_down("Enter")
            sleep(2)

            join_group_btn = get_element(
                tab,
                "x:(//button[@class='Button tiny primary has-ripple' and ./span[@class='inline-button-text'] and "
                "./div[@class='ripple-container']])[1]",
                5,
            )
            if join_group_btn:
                join_group_btn.click()
                sleep(0.5)

                join_btn = get_element(
                    tab, "x:(//button[@class='Button confirm-dialog-button default primary text'])[1]", 3
                )
                join_btn.click()
        return True

    def x_follow(self):
        sleep(2)
        latest_tab = self.browser_controller.page.latest_tab
        if "x.com" in latest_tab.url:
            latest_tab.close()
        logger.success(f"【{self.id}】推特关注成功（假任务）")
        return True

    def x_like(self):
        sleep(2)
        latest_tab = self.browser_controller.page.latest_tab
        if "x.com" in latest_tab.url:
            latest_tab.close()
        logger.success(f"【{self.id}】推特点赞成功（假任务）")
        return True

    def x_retweet(self):
        sleep(2)
        latest_tab = self.browser_controller.page.latest_tab
        if "x.com" in latest_tab.url:
            latest_tab.close()
        logger.success(f"【{self.id}】推特转发成功（假任务）")
        return True

    def x_comment(self):
        sleep(2)
        latest_tab = self.browser_controller.page.latest_tab
        if "x.com" in latest_tab.url:
            latest_tab.close()
        logger.success(f"【{self.id}】推特评论成功（假任务）")
        return True

    def x_task(self):
        sleep(2)
        latest_tab = self.browser_controller.page.latest_tab
        if "x.com" in latest_tab.url:
            latest_tab.close()
        logger.success(f"【{self.id}】推特任务成功（假任务）")
        return True

    def _filter_chain(self, tab, chain: str, xpath: str):
        logger.debug(f"【{self.id}】开始选择链")
        # chain_filter_btn_xpath = (
        #     "x:(//div[contains(@class, 'ProtocolSupplyBorrow_filters')]//button[contains(@class, 'Filter_button')])[1]"
        # )
        chain_filter_btn = get_element(tab, xpath, 5)
        if not chain_filter_btn:
            raise Exception(f"【{self.id}】未找到Networks按钮")
        chain_filter_btn.click()
        logger.debug(f"【{self.id}】正在点击Networks")
        sleep(0.5)

        chain_btn_xpath = f"x://button[./span[text()='{chain}']]"
        chain_btn = get_element(chain_filter_btn.next(), chain_btn_xpath, 5)
        if not chain_btn:
            raise Exception(f"【{self.id}】未找到{chain}按钮")
        chain_btn.click()
        logger.debug(f"【{self.id}】正在选择{chain}链")
        sleep(0.5)

    def _filter_protocol(self, tab, protocol: str, xpath: str):
        logger.debug(f"【{self.id}】开始选择协议")
        protocol_filter_btn_btn = get_element(tab, xpath, 5)
        if not protocol_filter_btn_btn:
            raise Exception(f"【{self.id}】未找到Protocols按钮")
        protocol_filter_btn_btn.click()
        logger.debug(f"【{self.id}】正在点击Protocols")
        sleep(0.5)
        if protocol == "aave":
            protocol = "Aave V3"
        if protocol == "compoundV3":
            protocol = "Compound V3"
        if protocol == "venus":
            protocol = "Venus"
        protocol_btn_xpath = f"x://button[./span[text()='{protocol}']]"
        protocol_btn = get_element(protocol_filter_btn_btn.next(), protocol_btn_xpath, 5)
        if not protocol_btn:
            raise Exception(f"【{self.id}】未找到{protocol}按钮")
        protocol_btn.click()
        logger.debug(f"【{self.id}】正在选择{protocol}协议")
        sleep(0.5)

    def _filter_asset(self, tab, xpath: str):
        """随机选择一个余额大于0的asset"""
        # 获取所有资产行
        logger.debug(f"【{self.id}】开始选择可用资产")
        asset_rows = get_elements(tab, xpath, 5)

        if not asset_rows:
            raise Exception(f"【{self.id}】未找到任何资产行")

        # 筛选余额大于0的资产
        valid_assets = []

        for row in asset_rows:
            try:
                # 在当前行中查找余额元素
                token_name = get_element(row, "x://span[contains(@class, 'ProtocolTableRow_assetToken')]", 3).text
                token_amount_text = get_element(row, "x://div[contains(@class, 'ProtocolTableRow_tokenLabel')]", 3).text
                token_amount = float(token_amount_text.replace(token_name, ""))
                if token_amount <= 0:
                    continue
                logger.debug(f"【{self.id}】找到{token_name}: {token_amount_text}")
                valid_assets.append({
                    "row": row,
                    "balance": token_amount,
                    "symbol": token_name,
                })
            except Exception as e:
                logger.warning(f"【{self.id}】处理资产行时出错: {e}")
                continue

        if not valid_assets:
            raise Exception(f"【{self.id}】未找到余额大于0的资产")

        # 随机选择一个有效资产
        selected_asset = random.choice(valid_assets)
        logger.info(f"【{self.id}】随机选择资产: {selected_asset['token']}, 余额: {selected_asset['balance']}")

        return selected_asset

    def _claim_tokens(self, claim_btn, tab):
        try:
            logger.debug(f"【{self.id}】开始执行任务: Claim Testnet Tokens To Perform The Next Tasks")
            tab.listen.start("https://app.soul.io/api/referral/validateMissionRequest")
            claim_btn.click()
            res = tab.listen.wait(timeout=90)
            if not res or not res.response.body:
                logger.error(f"【{self.id}】领水失败,未收到网络响应")
                return False
            logger.success(f"【{self.id}】领水成功")
            return True
        except Exception as e:
            logger.error(f"【{self.id}】领水异常, error={str(e)}")
            return False

    def _supply(self, chain: str, protocol: str) -> bool:
        """存款"""
        tab = self.browser_controller.page.latest_tab
        supply_btn_xpath = "x://button[@data-sentry-component='GradientButton' and .//span[text()='Supply']]"
        supply_btn = get_element(tab, supply_btn_xpath, 5)
        if not supply_btn:
            logger.error(f"【{self.id}】执行supply时未找到Supply按钮")
            return False
        logger.debug(f"【{self.id}】执行supply时正在点击Supply Button")
        supply_btn.click()
        sleep(2)

        chain_filter_btn_xpath = (
            "x:(//div[contains(@class, 'ProtocolSupplyBorrow_filters')]//button[contains(@class, 'Filter_button')])[1]"
        )
        self._filter_chain(tab, chain, chain_filter_btn_xpath)

        protocol_filter_btn_xpath = (
            "x:(//div[contains(@class, 'ProtocolSupplyBorrow_filters')]//button[contains(@class, 'Filter_button')])[2]"
        )
        self._filter_protocol(tab, protocol, protocol_filter_btn_xpath)

        # 随机选择一个余额大于0的资产
        asset_xpath = (
            "x://div[contains(@class, 'ProtocolSupplyBorrow_tables_')]//div[contains(@class, "
            "'ProtocolTableRow_supply')]"
        )
        asset = self._filter_asset(tab, asset_xpath)
        if not asset:
            logger.error(f"【{self.id}】执行supply时未发现可用资产")
            return False

        # 点击资产
        asset_row = asset["row"]
        asset_symbol = asset["symbol"]
        asset_balance = asset["balance"]
        asset_row.click()
        logger.debug(f"【{self.id}】执行supply时正在点击 {asset_symbol}")
        sleep(0.5)

        # 点击supply
        supply_action_xpath = "x://button[contains(@class, 'ActionBtn_supply') and .//span[text()='Supply']]"
        supply_action = get_element(tab, supply_action_xpath)
        supply_action.click()
        logger.debug(f"【{self.id}】执行supply时正在点击 Supply")
        sleep(0.5)

        # 输入金额, 金额取总数的1/4
        input_xpath = "x://div[contains(@class, 'ProtocolSupplyBorrow_panelContainerClassName')]//input"
        input_ele = get_element(tab, input_xpath, 3)
        input_ele.clear(True)

        supply_amount = asset_balance / 4
        input_ele.input(supply_amount)
        logger.debug(f"【{self.id}】准备supply：{supply_amount}{asset_symbol}")
        sleep(0.5)

        # 准备提交
        submit_xpath = (
            "x://div[contains(@class, 'ProtocolSupplyBorrow_panelContainerClassName')]//button[contains("
            "@class, 'ActionPanelSubmitBtn_supply')]"
        )
        submit_btn = get_element(tab, submit_xpath, 3)
        if "Insufficient" in submit_btn.html:
            supply_amount = supply_amount / 2
            input_ele.clear(True)
            input_ele.input(supply_amount)
            logger.info(f"【{self.id}】执行supply时余额不足，重新输入数量: {supply_amount}")
            sleep(0.5)
            return False

        if not submit_btn.states.is_clickable or not submit_btn.states.has_rect:
            logger.warning(f"【{self.id}】执行supply时提交按钮不可点击")
            return False

        submit_btn.click()
        if "Approve" in submit_btn.html:
            logger.info(f"【{self.id}】在 {chain} 上授权 {asset_balance}{asset_symbol}")
            if not self.browser_controller.okx_wallet_approve_amount(asset_balance):
                logger.error(f"【{self.id}】在 {chain} 上授权 {asset_balance}{asset_symbol} 失败")
                return False
            tab.wait.ele_displayed("x://span[contains(text(), 'Been Successful')]", 90)

        if not self.browser_controller.okx_wallet_sign():
            logger.error(f"【{self.id}】在 {chain} 上 Supply {supply_amount}{asset_symbol} 失败")
            return False

        if tab.wait.ele_displayed("x://span[contains(text(), 'Been Successful')]", 90):
            logger.success(f"【{self.id}】在 {chain} 上 Supply {supply_amount}{asset_symbol}")
            return True

        return False

    def _redeem(self, chain: str, protocol: str) -> bool:
        tab = self.browser_controller.page.latest_tab

        # 选择链
        chain_xpath = (
            "x://div[contains(@class, 'ProtocolFilters_root')]//button[contains(@class, 'Filter_button') "
            "and contains(., 'Networks')]"
        )
        self._filter_chain(tab, chain, chain_xpath)

        # 选择协议
        protocol_xpath = (
            "x://div[contains(@class, 'ProtocolFilters_root')]//button[contains(@class, 'Filter_button') "
            "and contains(., 'Protocols')]"
        )
        self._filter_protocol(tab, protocol, protocol_xpath)

        # 随机选择一个余额大于0的资产
        asset_xpath = (
            "x://div[text()='Supplied Positions']/ancestor::div[4]//div[contains(@class, "
            "'ProtocolTable_rows')]/div[contains(@class, 'ProtocolTableRow_root')]"
        )
        asset = self._filter_asset(tab, asset_xpath)
        if not asset:
            logger.error(f"【{self.id}】执行redeem时未发现可用资产")
            return False

        asset_row = asset["row"]
        asset_symbol = asset["symbol"]
        asset_row.click()
        logger.debug(f"【{self.id}】执行redeem时正在点击 {asset_symbol}")
        sleep(0.5)

        withdraw_action_xpath = "x://button[contains(@class, 'ActionBtn_supply') and .//span[text()='Withdraw']]"
        withdraw_action = get_element(tab, withdraw_action_xpath)
        withdraw_action.click()
        logger.debug(f"【{self.id}】执行redeem时正在点击 Withdraw")
        sleep(0.5)

        # 取最大值
        max_amount_xpath = "x://button[contains(@class, 'ActionPanelForm_maxAction') and .//span[text()='Max']]"
        max_amount = get_element(tab, max_amount_xpath)
        max_amount.click()
        sleep(0.5)
        logger.debug(f"【{self.id}】执行redeem时正在点击 Max")

        input_xpath = "x://div[contains(@class, 'ProtocolSupplyBorrow_panelContainerClassName')]//input"
        input_ele = get_element(tab, input_xpath, 3)
        withdraw_balance = input_ele.value
        logger.debug(f"【{self.id}】准备redeem：{withdraw_balance}{asset_symbol}")
        sleep(0.5)

        # 准备提交
        submit_xpath = (
            "x://div[contains(@class, 'ProtocolSupplyBorrow_panelContainerClassName')]//button[contains("
            "@class, 'ActionPanelSubmitBtn_supply')]"
        )
        submit_btn = get_element(tab, submit_xpath, 3)
        if "Insufficient" in submit_btn.html:
            logger.error(f"【{self.id}】执行redeem时余额不足")
            return False

        if not submit_btn.states.is_clickable or not submit_btn.states.has_rect:
            logger.warning(f"【{self.id}】执行redeem时提交按钮不可点击")
            return False

        submit_btn.click()
        sleep(2)

        if not self.browser_controller.okx_wallet_sign():
            logger.error(f"【{self.id}】在 {chain} 上 redeem {withdraw_balance}{asset_symbol}失败")
            return False

        if tab.wait.ele_displayed("x://span[contains(text(), 'Been Successful')]", 90):
            logger.success(f"【{self.id}】在 {chain} 上 redeem {withdraw_balance}{asset_symbol} ")
            return True

        return False

    def _add_collateral(self, chain: str, protocol: str) -> bool:
        tab = self.browser_controller.page.latest_tab

        # 选择链
        chain_xpath = (
            "x://div[contains(@class, 'ProtocolFilters_root')]//button[contains(@class, 'Filter_button') "
            "and contains(., 'Networks')]"
        )
        self._filter_chain(tab, chain, chain_xpath)

        # 选择协议
        protocol_xpath = (
            "x://div[contains(@class, 'ProtocolFilters_root')]//button[contains(@class, 'Filter_button') "
            "and contains(., 'Protocols')]"
        )
        self._filter_protocol(tab, protocol, protocol_xpath)

        # 随机选择一个余额大于0的资产
        asset_xpath = (
            "x://div[text()='Supplied Positions']/ancestor::div[4]//div[contains(@class, "
            "'ProtocolTable_rows')]/div[contains(@class, 'ProtocolTableRow_root')]"
        )
        asset = self._filter_asset(tab, asset_xpath)
        if not asset:
            logger.error(f"【{self.id}】未发现可用资产")
            return False

        # 选择资产
        asset_row = asset["row"]
        asset_symbol = asset["symbol"]
        asset_row.click()
        logger.debug(f"【{self.id}】执行addCollateral时正在点击 {asset_symbol}")
        sleep(0.5)

        collateral_action_xpath = "x://button[contains(@class, 'ActionBtn_supply') and .//span[text()='Collateral']]"
        collateral_action = get_element(tab, collateral_action_xpath)
        collateral_action.click()
        logger.debug(f"【{self.id}】执行addCollateral时正在点击 Collateral")
        sleep(0.5)

        add_action_xpath = "x://button[contains(@class, 'ActionBtn_add') and .//span[text()='Add']]"
        add_action = get_element(tab, add_action_xpath)
        add_action.click()
        logger.debug(f"【{self.id}】执行addCollateral时正在点击 Add")
        sleep(0.5)

        max_amount_xpath = "x://button[contains(@class, 'ActionPanelForm_maxAction') and .//span[text()='Max']]"
        max_amount = get_element(tab, max_amount_xpath)
        max_amount.click()
        logger.debug(f"【{self.id}】执行addCollateral时正在点击 Max")
        sleep(0.5)

        input_xpath = "x://div[contains(@class, 'ProtocolSupplyBorrow_panelContainerClassName')]//input"
        input_ele = get_element(tab, input_xpath, 3)
        add_collateral_amount = input_ele.value
        logger.info(f"【{self.id}】准备addCollateral: {add_collateral_amount}{asset_symbol}")

        # 准备提交
        submit_xpath = (
            "x://div[contains(@class, 'ProtocolSupplyBorrow_panelContainerClassName')]//button[contains("
            "@class, 'ActionPanelSubmitBtn_supply')]"
        )
        submit_btn = get_element(tab, submit_xpath, 3)
        if "Insufficient" in submit_btn.html:
            logger.info(f"【{self.id}】执行addCollateral时余额不足")
            return False

        if not submit_btn.states.is_clickable or not submit_btn.states.has_rect:
            logger.info(f"【{self.id}】执行addCollateral时提交按钮不可点击")
            return False

        submit_btn.click()
        sleep(2)

        if not self.browser_controller.okx_wallet_sign():
            logger.error(f"【{self.id}】在 {chain} 上 addCollateral {add_collateral_amount}{asset_symbol}失败")
            return False

        if tab.wait.ele_displayed("x://span[contains(text(), 'Been Successful')]", 90):
            logger.success(f"【{self.id}】在 {chain} 上 addCollateral {add_collateral_amount}{asset_symbol}成功")
            return True

        return False

    def _remove_collateral(self, chain: str, protocol: str) -> bool:
        tab = self.browser_controller.page.latest_tab

        # 选择链
        chain_xpath = (
            "x://div[contains(@class, 'ProtocolFilters_root')]//button[contains(@class, 'Filter_button') "
            "and contains(., 'Networks')]"
        )
        self._filter_chain(tab, chain, chain_xpath)

        # 选择协议
        protocol_xpath = (
            "x://div[contains(@class, 'ProtocolFilters_root')]//button[contains(@class, 'Filter_button') "
            "and contains(., 'Protocols')]"
        )
        self._filter_protocol(tab, protocol, protocol_xpath)

        # 随机选择一个余额大于0的资产
        asset_xpath = (
            "x://div[text()='Supplied Positions']/ancestor::div[4]//div[contains(@class, "
            "'ProtocolTable_rows')]/div[contains(@class, 'ProtocolTableRow_root')]"
        )
        asset = self._filter_asset(tab, asset_xpath)
        if not asset:
            logger.error(f"【{self.id}】未发现可用资产")
            return False

        asset_row = asset["row"]
        asset_symbol = asset["symbol"]
        asset_row.click()
        logger.debug(f"【{self.id}】执行removeCollateral时正在点击 {asset_symbol}")
        sleep(0.5)

        collateral_action_xpath = "x://button[contains(@class, 'ActionBtn_supply') and .//span[text()='Collateral']]"
        collateral_action = get_element(tab, collateral_action_xpath)
        collateral_action.click()
        logger.debug(f"【{self.id}】执行removeCollateral时正在点击 Collateral")
        sleep(0.5)

        remove_action_xpath = "x://button[contains(@class, 'ActionBtn_remove') and .//span[text()='Remove']]"
        remove_action = get_element(tab, remove_action_xpath)
        logger.debug(f"【{self.id}】执行removeCollateral时正在点击 Remove")
        remove_action.click()
        sleep(0.5)

        # 取最大数量
        max_amount_xpath = "x://button[contains(@class, 'ActionPanelForm_maxAction') and .//span[text()='Max']]"
        max_amount = get_element(tab, max_amount_xpath)
        max_amount.click()
        logger.debug(f"【{self.id}】执行removeCollateral时正在点击 Max")
        sleep(0.5)

        input_xpath = "x://div[contains(@class, 'ProtocolSupplyBorrow_panelContainerClassName')]//input"
        input_ele = get_element(tab, input_xpath, 3)
        remove_collateral_amount = input_ele.value
        logger.info(f"【{self.id}】准备removeCollateral: {remove_collateral_amount}{asset_symbol}")

        # 准备提交
        submit_xpath = (
            "x://div[contains(@class, 'ProtocolSupplyBorrow_panelContainerClassName')]//button[contains("
            "@class, 'ActionPanelSubmitBtn_supply')]"
        )
        submit_btn = get_element(tab, submit_xpath, 3)

        if "Insufficient" in submit_btn.html:
            logger.info(f"【{self.id}】执行removeCollateral时余额不足")
            return False

        if not submit_btn.states.is_clickable or not submit_btn.states.has_rect:
            logger.info(f"【{self.id}】执行removeCollateral时提交按钮不可点击")
            return False

        submit_btn.click()
        sleep(2)

        if not self.browser_controller.okx_wallet_sign():
            logger.error(f"【{self.id}】在 {chain} 上 removeCollateral {remove_collateral_amount}{asset_symbol}失败")
            return False

        if tab.wait.ele_displayed("x://span[contains(text(), 'Been Successful')]", 90):
            logger.success(f"【{self.id}】在 {chain} 上 removeCollateral {remove_collateral_amount}{asset_symbol}成功")
            return True

        return False

    def _borrow(self, chain: str, protocol: str) -> bool:
        tab = self.browser_controller.page.latest_tab
        borrow_btn_xpath = "x://button[@data-sentry-component='GradientButton' and .//span[text()='Borrow']]"
        borrow_btn = get_element(tab, borrow_btn_xpath, 5)
        if not borrow_btn:
            logger.error(f"【{self.id}】执行borrow时未找到Borrow按钮")
            return False
        logger.debug(f"【{self.id}】执行borrow时正在点击Borrow Button")
        borrow_btn.click()
        sleep(1)

        chain_filter_btn_xpath = (
            "x:(//div[contains(@class, 'ProtocolSupplyBorrow_filters')]//button[contains(@class, 'Filter_button')])[1]"
        )
        self._filter_chain(tab, chain, chain_filter_btn_xpath)

        protocol_filter_btn_xpath = (
            "x:(//div[contains(@class, 'ProtocolSupplyBorrow_filters')]//button[contains(@class, 'Filter_button')])[2]"
        )
        self._filter_protocol(tab, protocol, protocol_filter_btn_xpath)

        # 随机选择一个余额大于0的资产
        asset = self._filter_asset(tab, "borrow")
        if not asset:
            logger.error(f"【{self.id}】未发现可用资产")
            return False

        asset_row = asset["row"]
        asset_symbol = asset["symbol"]
        asset_row.click()
        logger.debug(f"【{self.id}】执行borrow时正在点击 {asset_symbol}")
        sleep(0.5)

        borrow_action_xpath = "x://button[contains(@class, 'ActionBtn_borrow') and .//span[text()='Borrow']]"
        borrow_action = get_element(tab, borrow_action_xpath)
        borrow_action.click()
        logger.debug(f"【{self.id}】执行borrow时正在点击 Borrow")
        sleep(0.5)

        # 取最大数量
        max_amount_xpath = "x://button[contains(@class, 'ActionPanelForm_maxAction') and .//span[text()='Max']]"
        max_amount = get_element(tab, max_amount_xpath)
        max_amount.click()
        logger.debug(f"【{self.id}】执行borrow时正在点击 Max")
        sleep(0.5)

        input_xpath = "x://div[contains(@class, 'ProtocolSupplyBorrow_panelContainerClassName')]//input"
        input_ele = get_element(tab, input_xpath, 3)
        borrow_amount = input_ele.value
        logger.info(f"【{self.id}】准备borrow: {borrow_amount}{asset_symbol}")

        # 准备提交
        submit_xpath = (
            "x://div[contains(@class, 'ProtocolSupplyBorrow_panelContainerClassName')]//button[contains("
            "@class, 'ActionPanelSubmitBtn_borrow')]"
        )
        submit_btn = get_element(tab, submit_xpath, 3)

        if "Insufficient" in submit_btn.html:
            logger.info(f"【{self.id}】执行borrow时余额不足")
            return False

        if not submit_btn.states.is_clickable or not submit_btn.states.has_rect:
            logger.info(f"【{self.id}】执行borrow时提交按钮不可点击")
            return False

        submit_btn.click()
        sleep(2)

        if not self.browser_controller.okx_wallet_sign():
            logger.error(f"【{self.id}】在 {chain} 上 borrow {borrow_amount}{asset_symbol}失败")
            return False

        if tab.wait.ele_displayed("x://span[contains(text(), 'Been Successful')]", 90):
            logger.success(f"【{self.id}】在 {chain} 上 borrow {borrow_amount}{asset_symbol}成功")
            return True

        return False

    def _repay_borrow(self, chain: str, protocol: str) -> bool:
        tab = self.browser_controller.page.latest_tab

        # 选择链
        chain_xpath = (
            "x://div[contains(@class, 'ProtocolFilters_root')]//button[contains(@class, 'Filter_button') "
            "and contains(., 'Networks')]"
        )
        self._filter_chain(tab, chain, chain_xpath)

        # 选择协议
        protocol_xpath = (
            "x://div[contains(@class, 'ProtocolFilters_root')]//button[contains(@class, 'Filter_button') "
            "and contains(., 'Protocols')]"
        )
        self._filter_protocol(tab, protocol, protocol_xpath)

        # 随机选择一个余额大于0的资产
        asset_xpath = (
            "x://div[text()='Borrowed Positions']/ancestor::div[4]//div[contains(@class, "
            "'ProtocolTable_rows')]/div[contains(@class, 'ProtocolTableRow_root')]"
        )
        asset = self._filter_asset(tab, asset_xpath)
        if not asset:
            logger.error(f"【{self.id}】未发现可用资产")
            return False

        asset_row = asset["row"]
        asset_symbol = asset["symbol"]
        asset_row.click()
        logger.debug(f"【{self.id}】执行repayBorrow时正在点击 {asset_symbol}")
        sleep(0.5)

        repay_action_xpath = "x://button[contains(@class, 'ActionBtn_borrow') and .//span[text()='Repay']]"
        repay_action = get_element(tab, repay_action_xpath)
        repay_action.click()
        logger.debug(f"【{self.id}】执行repayBorrow时正在点击 repay")
        sleep(0.5)

        # 取最大数量
        max_amount_xpath = "x://button[contains(@class, 'ActionPanelForm_maxAction') and .//span[text()='Max']]"
        max_amount = get_element(tab, max_amount_xpath)
        max_amount.click()
        logger.debug(f"【{self.id}】执行repayBorrow时正在点击 Max")
        sleep(0.5)

        input_xpath = "x://div[contains(@class, 'ProtocolSupplyBorrow_panelContainerClassName')]//input"
        input_ele = get_element(tab, input_xpath, 3)
        repay_amount = input_ele.value
        logger.info(f"【{self.id}】准备repay: {repay_amount}{asset_symbol}")

        # 准备提交
        submit_xpath = (
            "x://div[contains(@class, 'ProtocolSupplyBorrow_panelContainerClassName')]//button[contains("
            "@class, 'ActionPanelSubmitBtn_borrow')]"
        )
        submit_btn = get_element(tab, submit_xpath, 3)

        if "Insufficient" in submit_btn.html:
            logger.info(f"【{self.id}】执行repayBorrow时余额不足")
            return False

        if not submit_btn.states.is_clickable or not submit_btn.states.has_rect:
            logger.info(f"【{self.id}】执行repayBorrow时提交按钮不可点击")
            return False

        submit_btn.click()
        sleep(2)

        if not self.browser_controller.okx_wallet_sign():
            logger.success(f"【{self.id}】在 {chain} 上 repayBorrow {repay_amount}{asset_symbol}失败")
            return False

        if tab.wait.ele_displayed("x://span[contains(text(), 'Been Successful')]", 90):
            logger.success(f"【{self.id}】在 {chain} 上 repayBorrow {repay_amount}{asset_symbol}成功")
            return True

        return False

    def _mission(self, mission_name: str, mission_type: str, chain: str, operation: str, protocol: str):
        try:
            chain_mission = {
                "supply": self._supply,
                "redeem": self._redeem,
                "borrow": self._borrow,
                "repayBorrow": self._repay_borrow,
                "addCollateral": self._add_collateral,
                "removeCollateral": self._remove_collateral,
            }
            social_mission = {
                "CONNECT_X": self._x_connect,
                "SOCIAL_X_FOLLOW": self.x_follow,
                "SOCIAL_X_RETWEET": self.x_retweet,
                "SOCIAL_X_COMMENT": self.x_comment,
                "SPECIAL_X": self.x_task,
                "CONNECT_TELEGRAM": self._telegram_connect,
                "CONNECT_DISCORD": self._discord_connect,
                "SOCIAL_DISCORD": self._discord_join,
            }
            if mission_type == "ON_CHAIN":
                logger.debug(f"【{self.id}】在 {chain} 链的 {protocol} 协议上执行 {operation} 操作")
                logger.debug(f"【{self.id}】等待页面加载...(15秒)")
                self.browser_controller.page.latest_tab.wait.ele_displayed("x://*[@class='is-loaded']", timeout=20)
                return chain_mission[operation](chain, protocol)
            elif "SOCIAL" in mission_type or "CONNECT" in mission_type or mission_type == "SPECIAL_X":
                return social_mission[mission_type]()
            else:
                logger.warning(f"【{self.id}】暂不支持的任务类型：{mission_type}")
                return False
        except Exception as e:
            logger.error(f"【{self.id}】执行任务 {mission_name} 发生异常, error={str(e)}")
            return False

    def _quest00(self):
        record = self.data_util.get(self.address)
        quest = get_quest("quest00")
        if record.get("quest00_status", "") == "1":
            return True
        return self._execute_quest(quest)

    def _quest0(self) -> bool:
        record = self.data_util.get(self.address)
        quest = get_quest("quest0")
        if record.get("quest0_status", "") == "1":
            return True
        return self._execute_quest(quest)

    def _quest1(self):
        record = self.data_util.get(self.address)
        quest = get_quest("quest1")
        if record.get("quest1_status", "") == "1":
            return True
        return self._execute_quest(quest)

    def _quest2(self):
        record = self.data_util.get(self.address)
        quest = get_quest("quest2")
        if record.get("quest2_status", "") == "1":
            return True
        return self._execute_quest(quest)

    def _quest3(self):
        record = self.data_util.get(self.address)
        quest = get_quest("quest3")
        if record.get("quest3_status", "") == "1":
            return True
        return self._execute_quest(quest)

    def _quest4(self):
        record = self.data_util.get(self.address)
        quest = get_quest("quest4")
        if record.get("quest4_status", "") == "1":
            return True
        return self._execute_quest(quest)

    def _quest5(self):
        record = self.data_util.get(self.address)
        quest = get_quest("quest5")
        if record.get("quest5_status", "") == "1":
            return True

        return self._execute_quest(quest)

    def _quest6(self):
        record = self.data_util.get(self.address)
        quest = get_quest("quest6")
        if record.get("quest6_status", "") == "1":
            return True
        return self._execute_quest(quest)

    def _execute_quest(self, quest: dict):
        key = quest.get("key")
        name = quest.get("name")
        tab = self.browser_controller.page.latest_tab
        record = self.data_util.get(self.address)
        if record.get(f"{key}_status", "") == "1":
            logger.success(f"【{self.id}】{name} 已全部完成")
            return True

        logger.info(f"【{self.id}】开始执行任务: {quest.get('name')}")

        quest_xpath = f'x://div[text()="{name}"]/ancestor::div[3]/child::div[5]//button'
        quest_btn = get_element(tab, quest_xpath, 5)
        if not quest_btn:
            logger.error(f"【{self.id}】未找到任务 {name} 的开始按钮")
            return False

        if "Complete" in quest_btn.html:
            logger.success(f"【{self.id}】{name} 已全部完成")
            update_data = {f"{key}_status": 1}
            for mission in quest.get("missions"):
                update_data[f"{key}_{mission.get('key')}"] = 1

            self.data_util.update(self.address, update_data)
            return True

        if not quest_btn.states.is_clickable or not quest_btn.states.has_rect:
            logger.warning(f"【{self.id}】前一个任务未完成，任务 {name} 不可点击")
            return False

        quest_btn.click()
        sleep(0.5)

        missions = quest.get("missions")
        for mission in missions:
            mission_key = mission.get("key")
            mission_name = mission.get("name")
            mission_type = mission.get("type")
            chain = mission.get("chain")
            operation = mission.get("operation")
            protocol = mission.get("protocol")
            try:
                if record.get(f"{key}_{mission_key}", "") == "1":
                    logger.success(f"【{self.id}】{key}: {mission_name} 已完成")
                    continue

                mission_xpath = f'x://p[text()="{mission_name}"]/parent::div[1]/following-sibling::div[4]'
                mission_btn = get_element(tab, mission_xpath, 5)
                if not mission_btn:
                    logger.error(f"【{self.id}】未找到任务 {mission_name} 的开始按钮")
                    continue
                if "Complete" in mission_btn.html:
                    logger.success(f"【{self.id}】{key}:{mission_name} 已完成")
                    self.data_util.update(self.address, {f"{key}_{mission_key}": 1})
                    continue
                if "Locked" in mission_btn.html:
                    logger.warning(f"【{self.id}】前一个任务未完成，任务 {mission_name} 不可点击")
                    return False
                if "Faucet" in mission_btn.html:
                    self._claim_tokens(mission_btn, tab)
                if "Verify code" in mission_btn.html:
                    self._telegram_connect()
                if "Claiming" in mission_btn.html:
                    logger.success(f"【{self.id}】已领水未到账")
                    return False
                if "Claimed" in mission_btn.html:
                    logger.success(f"【{self.id}】任务 {mission_name} 已领取")
                    self.data_util.update(self.address, {f"{key}_{mission_key}": "1"})
                    continue
                is_verify = True
                if "Access App" in mission_btn.html or "Connect" in mission_btn.html:
                    mission_btn.click()
                    logger.info(f"【{self.id}】开始执行任务: {mission_name}")
                    is_verify = self._mission(mission_name, mission_type, chain, operation, protocol)
                    sleep(5)
                    tab = self.browser_controller.page.latest_tab
                if is_verify and "Verify" in mission_btn.html:
                    logger.info(f"【{self.id}】开始验证任务: {key}: {mission_name}")
                    tab = self.browser_controller.page.latest_tab
                    tab.listen.start("https://app.soul.io/api/referral/validateMission")
                    mission_btn.click()
                    res = tab.listen.wait(timeout=90)
                    if not res or not res.response.body:
                        logger.error(f"【{self.id}】任务 {mission_name} 验证失败,未收到网络响应")
                        continue
                    elif res.response.body.get("success", False):
                        logger.success(f"【{self.id}】任务 {mission_name} 验证成功")
                        self.data_util.update(self.address, {f"{key}_{mission_key}": "1"})
                        sleep(5)
                    else:
                        logger.error(f"【{self.id}】任务 {mission_name} 验证失败")
                        continue
                if "Complete" in mission_btn.html:
                    logger.success(f"【{self.id}】{key}:{mission_name} 已完成")
                    self.data_util.update(self.address, {f"{key}_{mission_key}": 1})
                    continue
                sleep(5)
            except Exception as e:
                logger.error(f"【{self.id}】执行任务 {mission_name} 发生异常, error={str(e)}")
                continue

        tab.refresh()
        logger.debug(f"【{self.id}】等待页面loading...")
        tab.wait.ele_displayed("x://*[@class='is-loaded']", timeout=20)
        sleep(5)
        return True

    def quest(self, count: int):
        for i in range(count):
            logger.info(f"【{self.id}】开始执行第 {i + 1} 次任务")
            record = self.data_util.get(self.address)
            if record.get("status", "") == "1":
                logger.info(f"【{self.id}】已完成全部任务")
                return True

            uncompleted_quests = []
            # if record.get("quest0_status", "") != "1":
            #     uncompleted_quests.append(self._quest0)

            if record.get("quest1_status", "") != "1":
                uncompleted_quests.append(self._quest1)

            if record.get("quest00_status", "") != "1":
                uncompleted_quests.append(self._quest00)

            if record.get("quest2_status", "") != "1":
                uncompleted_quests.append(self._quest2)

            if record.get("quest3_status", "") != "1":
                uncompleted_quests.append(self._quest3)

            if record.get("quest4_status", "") != "1":
                uncompleted_quests.append(self._quest4)

            if record.get("quest5_status", "") != "1":
                uncompleted_quests.append(self._quest5)

            if record.get("quest6_status", "") != "1":
                uncompleted_quests.append(self._quest6)

            if len(uncompleted_quests) == 0:
                self.data_util.update(self.address, {"status": 1})
                logger.success(f"【{self.id}】已完成全部任务")
                return True

            for quest in uncompleted_quests:
                if not quest():
                    break

        # 更新积分排名
        self.rank()
        return True

    def execute(self, count: int) -> bool:
        logger.info(f"【{self.id}】开始执行任务")
        # if not self.browser_controller.browser_config.tg_cellphone:
        #     logger.info(f"【{self.id}】未配置Telegram, 跳过...")
        #     return True

        # 1、登录OKX钱包
        if not self._setup_browser():
            return False

        # 2、登录Soul
        if not self.login():
            logger.error(f"【{self.id}】登录失败")
            return False

        # 3. 执行任务
        return self.quest(count)


def _get_referral_code(browser_type, index, data_util):
    soul = None
    try:
        soul = SoulProtocol(browser_type, str(index), data_util)
        return soul.get_referral_code()
    except Exception as e:
        logger.error(f"{index} 执行任务异常 {e}")
        return False
    finally:
        if soul:
            soul.browser_controller.close_page()


def _get_data_util(browser_type: BrowserType) -> DataUtil:
    """
    获取数据工具实例

    Returns:
        DataUtil: 数据工具实例
    """
    data_dir = os.path.join(get_project_root_path(), "examples", "soul")
    csv_path = os.path.join(data_dir, f"soul_{browser_type.name.lower()}.csv")
    return DataUtil(csv_path)


def get_quest(quest_key: str):
    """
    从 soul_quest.json 获取 quest 数据

    Args:
        quest_key: quest 的 key，如 'quest0', 'quest1', 'quest2' 等

    Returns:
        quest 对象，如果未找到则返回 None
    """
    try:
        quest_file_path = os.path.join(os.path.dirname(__file__), "soul_quest.json")
        with open(quest_file_path, "r", encoding="utf-8") as f:
            quest_data = json.load(f)

        # 直接通过 key 获取 quest
        if quest_key in quest_data:
            return quest_data[quest_key]

        logger.warning(f"Quest not found: {quest_key}")
        return None

    except FileNotFoundError:
        logger.error(f"Quest file not found")
        return None
    except json.JSONDecodeError as e:
        logger.error(f"Invalid JSON in quest file: {e}")
        return None
    except Exception as e:
        logger.error(f"Error loading quest data: {e}")
        return None


def _execute_task(browser_type, index, data_util):
    try:
        soul = SoulProtocol(browser_type, str(index), data_util)
        return soul.execute(3)
    except Exception as e:
        logger.error(f"{index} 执行任务异常 {e}")
        return False


@click.group()
def cli():
    pass


@cli.command("code")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
def get_referral_code(type, index, workers):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)
        successful_indices = []
        failed_indices = []

        data_util = _get_data_util(type)

        def process_task(index):
            result = _get_referral_code(type, index, data_util)
            if result:
                successful_indices.append(index)
            else:
                failed_indices.append(index)

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=1200,  # 20分钟超时
            retries=3,
            interval=10,
            task_name=f"Soul-{type}",
            raise_exception=False,
        )

        # 批量执行任务
        executor.run_batch(process_task, indices)
        # 计算失败列表
        failed_indices = list(set(indices) - set(successful_indices))  # 计算失败的索引
        # 如果需要保持列表格式
        failed_indices = [str(index) for index in failed_indices]
        logger.success(
            f"本次共执行 {len(indices)} 个账号，成功 {len(successful_indices)} 个，失败 {len(failed_indices)} 个"
        )
        if len(failed_indices) > 0:
            logger.success(f"失败的账号列表: {','.join(failed_indices)}")
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


@cli.command("run")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
@click.option("-c", "--count", type=int, default=3, help="执行次数")
def run(type, index, workers, count):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)
        successful_indices = []  # 新增成功列表

        data_util = _get_data_util(type)

        def process_task(idx: int) -> bool:
            soul = None
            try:
                soul = SoulProtocol(type, str(idx), data_util)
                if soul.data_util.get(soul.address).get("status", "") == "1":
                    logger.info(f"【{index}】已完成全部任务")
                    successful_indices.append(idx)
                    return True
                result = soul.execute(count)
                if result:
                    successful_indices.append(idx)
                return result
            except Exception as e:
                logger.error(f"【{idx}】执行任务异常: {str(e)}")
                return False
            finally:
                if soul:
                    soul.browser_controller.close_page()

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=6 * 3600,  # 6小时超时
            retries=3,
            interval=10,
            task_name=f"Soul_{type}",
            raise_exception=False,
        )

        # 批量执行任务
        executor.run_batch(process_task, indices)
        # 计算失败列表
        failed_indices = list(set(indices) - set(successful_indices))  # 计算失败的索引
        # 如果需要保持列表格式
        failed_indices = [str(index) for index in failed_indices]
        logger.success(
            f"本次共执行 {len(indices)} 个账号，成功 {len(successful_indices)} 个，失败 {len(failed_indices)} 个"
        )
        if len(failed_indices) > 0:
            logger.success(f"失败的账号列表: {','.join(failed_indices)}")
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


if __name__ == "__main__":
    cli()
    # _execute_task(BrowserType.ADS, "43", _get_data_util(BrowserType.ADS))
    # _get_referral_code(BrowserType.ADS, "4", _get_data_util(BrowserType.ADS))
