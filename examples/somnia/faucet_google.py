
import os
from datetime import datetime, date, timedelta

from time import sleep

import click
from loguru import logger

from config import DEFAULT_BROWSER_TYPE
from src.browsers import BROWSER_TYPES, try_click
from src.controllers import Browser<PERSON>ontroller
from src.enums.browsers_enums import BrowserType
from src.utils import get_element
from src.utils.common import get_project_root_path, parse_indices
from src.utils.data_utils import DataUtil
from src.utils.thread_executor import ThreadExecutor

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]
logger.add("logs/faucet_google.log", rotation="10MB", level="SUCCESS")


class FaucetOnGoogle:
    def __init__(self, browser_type: BrowserType, index: str, data_util: DataUtil):
        self.task_name = None
        self.task_url = None
        self.id = index
        self.browser_type = browser_type
        self.browser_controller = BrowserController(browser_type, index)
        self.address = self.browser_controller.browser_config.evm_address
        self.data_util = data_util
        self._init_data()

    def _init_data(self):
        try:
            data = self.data_util.get(self.address)
            if data:
                return

            data = {
                "index": self.id,
                "type": self.browser_type.value,
                "address": self.address,
            }
            self.data_util.add(data)
        except Exception as e:
            logger.error(f"【{self.id}】{self.address} 初始化数据失败: {e}")

    def _can_faucet(self):
        faucet_time = self.data_util.get(self.address).get("faucet_time", "")
        if faucet_time:
            last_date = datetime.strptime(faucet_time, "%Y-%m-%d %H:%M:%S").date()
            current_date = date.today()
            return (current_date - last_date) > timedelta(days=1)
        return True


    def faucet(self):
        try:
            if not self._can_faucet():
                logger.warning(f"【{self.id}】24小时内已经领取过，请24小时后再来")
                return True
            url = "https://cloud.google.com/application/web3/faucet/somnia/shannon"
            for i in range(3):
                logger.info(f"【{self.id}】第 {i+1} 次领水")
                tab = None
                try:
                    tab = self.browser_controller.page.new_tab(url)
                    input_i = get_element(tab, "x://input[@id='mat-input-0']", 10)
                    if input_i:
                        tab.actions.move_to(input_i).click().type(self.address).key_down("Enter")
                        if tab.wait.ele_displayed("x://mat-card[@data-test-id='drip-status-card']", 15):
                            logger.success(f"【{self.id}】领水成功")
                            self.data_util.update(self.address, {"faucet_time":datetime.now().strftime("%Y-%m-%d %H:%M:%S")})
                            return True
                except Exception as e:
                    logger.error(f"【{self.id}】领水异常, error={str(e)}")
                finally:
                    tab.close()
            logger.error(f"【{self.id}】领水失败")
            return False
        finally:
            if self.browser_controller:
                self.browser_controller.close_page()



def _get_data_util(browser_type: BrowserType) -> DataUtil:
    """
    获取数据工具实例

    Returns:
        DataUtil: 数据工具实例
    """

    data_dir = os.path.join(get_project_root_path(), "examples", "somnia")
    csv_path = os.path.join(data_dir, f"faucet_google_{browser_type.name.lower()}.csv")
    return DataUtil(csv_path)


@click.group()
def cli():
    pass


@cli.command("run")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
def run(type, index, workers):
    try:
        indices = parse_indices(index)
        data_util = _get_data_util(type)

        successful_indices = []
        failed_indices = []


        def process_task(idx):
            google = FaucetOnGoogle(type, idx, data_util)
            result = google.faucet()
            if result:
                successful_indices.append(idx)
            else:
                failed_indices.append(idx)
            return result

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=6 * 3600,  # 6小时超时
            retries=3,
            interval=10,
            task_name=f"giveaway_{type}",
            raise_exception=False,
        )

        # 批量执行任务
        executor.run_batch(process_task, indices)
        # 计算失败列表
        failed_indices = list(set(indices) - set(successful_indices))  # 计算失败的索引
        # 如果需要保持列表格式
        failed_indices = [str(index) for index in failed_indices]
        logger.success(
            f"本次共执行 {len(indices)} 个账号，成功 {len(successful_indices)} 个，失败 {len(failed_indices)} 个"
        )
        if len(failed_indices) > 0:
            logger.success(f"失败的账号列表: {','.join(failed_indices)}")
    except Exception as e:
        logger.error(f"领水发生异常: {e}")


if __name__ == "__main__":
    cli()
