import logging
import os
import random
import sys
import time

import click
import requests
from config_pharo import (
    DEFAULT_BROWSER_TYPE,
    PROXY_CHECK,
    TASK_INTERVAL,
    TASK_RETRIES,
    TASK_TIMEOUT,
)
from data_manager import DataManager
from logger_config import configure_logger
from loguru import logger
from pharo_task import PharoTask

from src.browsers import BROWSER_TYPES
from src.browsers.config import get_browser_extension_id
from src.enums.browsers_enums import BrowserType
from src.utils.common import parse_indices
from src.utils.thread_executor import ThreadExecutor
from utils import measure_time

# 初始化日志配置
configure_logger()


def run_task(type: str, index: str) -> bool:
    """运行单个任务

    Args:
        type: 浏览器类型
        index: 任务索引

    Returns
    -------
        bool: 是否执行成功
    """
    try:
        pharo_task = PharoTask(type, str(index))
        #  extension_id = get_browser_extension_id("yescaptcha", type)

        # pharo_task.browser_controller.okx_wallet_login()
        if not check_and_update_proxy_by_id(type, index):
            pharo_task.logger.error("网络问题，请检查")
            return False
        # 先注册
        if pharo_task.register(type):
            # 注册成功后执行任务
            result = pharo_task.execute_tasks()
            #    pharo_task.browser_controller.chrome_extension_status(extension_id, False)
            if result:
                return True
        else:
            pharo_task.logger.error("注册失败")
            return False
    except Exception as e:
        pharo_task.logger.error(f"执行任务异常: {e}")
        return False


def test_proxy(proxy: str) -> bool:
    """测试代理是否可用

    Args:
        proxy: 代理地址，格式如 socks5://127.0.0.1:7890 或 http://127.0.0.1:7890

    Returns
    -------
        bool: 代理是否可用
    """
    if not proxy:
        return False

    try:
        # 确保代理格式正确
        if not proxy.startswith(("http://", "https://", "socks5://")):
            proxy = f"http://{proxy}"

        # 转换代理协议
        proxy = proxy.replace("socks5://", "http://")

        # 发送请求
        response = requests.get("https://www.okx.com/", proxies={"http": proxy, "https": proxy}, timeout=10)

        return response.status_code == 200
    except requests.exceptions.ProxyError:
        logger.error(f"代理连接错误: {proxy}")
        return False
    except requests.exceptions.ConnectTimeout:
        logger.error(f"代理连接超时: {proxy}")
        return False
    except requests.exceptions.RequestException as e:
        logger.error(f"代理请求异常: {proxy}, 错误: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"代理测试未知错误: {proxy}, 错误: {str(e)}")
        return False


def check_and_update_proxy_by_id(type: str, id: str) -> bool:
    """
    根据ID检查代理是否可用，如果不可用则更新代理

    Args:
        type: 浏览器类型
        id: 配置ID

    Returns
    -------
        bool: 代理是否可用或更新成功
    """
    from src.browsers.config import get_browser_data_path

    path = get_browser_data_path(type)
    filename = os.path.basename(path)

    data_manager = DataManager(project_name=filename, base_path="data")
    configs = data_manager.get_by_id(str(id))

    if configs and not test_proxy(proxy=configs["proxy"]):
        logger.info("代理不可用，更新代理ing")
        from src.utils.ip_update import IPManager

        ip_manager = IPManager(config_csv=path)
        if not ip_manager.check_and_update_proxy_by_id(str(id)):
            logger.error("代理更新失败")
            return False
        logger.info("代理更新成功")
        # 等待一段时间让代理生效
        time.sleep(5)
        # 再次测试代理是否可用
        configs = data_manager.get_by_id(str(id))
        if not test_proxy(proxy=configs["proxy"]):
            logger.error("代理更新后仍然不可用")
            return False
        logger.info("代理更新后可用")
        return True
    return True


@click.group()
def cli():
    """命令行工具"""
    pass


def process_task_results(
    results: list,
    indices: list,
    successful_indices: list,
    failed_proxy_ids: list,
    failed_discord_ids: list,
    failed_twitter_ids: list,
) -> None:
    """处理任务执行结果并输出统计信息

    Args:
        results: 任务执行结果列表
        indices: 所有任务索引列表
        successful_indices: 成功执行的任务索引列表
        failed_proxy_ids: 代理异常的任务索引列表
        failed_discord_ids: Discord绑定失败的任务索引列表
        failed_twitter_ids: Twitter绑定失败的任务索引列表
    """
    # 计算失败的索引
    failed_indices = list(set(indices) - set(successful_indices))
    failed_indices = [str(index) for index in failed_indices]

    # 输出总体执行结果
    logger.info(f"任务执行结果: {results}")
    logger.info(f"成功索引: {','.join(map(str, successful_indices))}")
    logger.info(f"失败索引: {','.join(failed_indices)}")

    # 从过滤后的失败索引中移除特定类型的失败
    filtered_failed = [
        idx for idx in failed_indices if idx not in map(str, failed_proxy_ids + failed_discord_ids + failed_twitter_ids)
    ]
    logger.info(f"可重试失败索引（不包含下列三种异常）: {','.join(filtered_failed)}")

    # 输出特定类型的失败
    if failed_proxy_ids:
        logger.error(f"代理异常索引: {','.join(map(str, failed_proxy_ids))}")
    if failed_discord_ids:
        logger.error(f"Discord绑定失败索引: {','.join(map(str, failed_discord_ids))}")
    if failed_twitter_ids:
        logger.error(f"Twitter绑定失败索引: {','.join(map(str, failed_twitter_ids))}")


def execute_single_task(
    type: str,
    index: int,
    pharo_tasks: dict,
    active_tasks: set,
    successful_indices: list,
    failed_indices: list,
    failed_proxy_ids: list,
    failed_discord_ids: list,
    failed_twitter_ids: list,
    completed_tasks: int,
    total_tasks: int,
    proxy_check: bool = False,
) -> bool:
    """执行单个任务

    Args:
        type: 浏览器类型
        index: 任务索引
        pharo_tasks: 任务管理器实例字典
        active_tasks: 活动任务集合
        successful_indices: 成功索引列表
        failed_indices: 失败索引列表
        failed_proxy_ids: 代理异常索引列表
        failed_discord_ids: Discord绑定失败索引列表
        failed_twitter_ids: Twitter绑定失败索引列表
        completed_tasks: 已完成任务数
        total_tasks: 总任务数
        proxy_check: 是否开启代理检测

    Returns
    -------
        bool: 任务是否执行成功
    """
    pharo_task = None
    try:
        active_tasks.add(index)
        pharo_task = PharoTask(type, str(index))
        pharo_tasks[index] = pharo_task  # 保存任务管理器实例

        # 检查并更新代理，如果失败则返回False触发重试
        if proxy_check and not check_and_update_proxy_by_id(type, index):
            pharo_task.logger.error("网络问题，请检查")
            failed_proxy_ids.append(index)
            # 关闭当前浏览器实例，让重试时创建新的实例
            if pharo_task:
                try:
                    pharo_task.browser_controller.close_page()
                except Exception as e:
                    pharo_task.logger.debug(f"关闭浏览器失败: {str(e)}")
            return False

        pharo_task.browser_controller.okx_wallet_login()

        # 先执行注册
        if not pharo_task.register(type):
            pharo_task.logger.error("注册失败")
            return False
        pharo_task.browser_controller.window_max()
        result = pharo_task.execute_tasks()
        if result:
            successful_indices.append(index)
        else:
            failed_indices.append(index)
            # 记录绑定失败的账号
            if pharo_task.discord_bind_failed:
                failed_discord_ids.append(index)
            if pharo_task.twitter_bind_failed:
                failed_twitter_ids.append(index)
        return result
    except Exception as e:
        if pharo_task:
            pharo_task.logger.error(f"处理任务失败: {str(e)}")
        else:
            logger.error(f"处理任务 {index} 失败: {str(e)}")
        failed_indices.append(index)
        return False
    finally:
        active_tasks.remove(index)
        completed_tasks += 1
        # 显示进度
        progress = (completed_tasks / total_tasks) * 100
        logger.info(f"进度: {progress:.1f}% ({completed_tasks}/{total_tasks})")
        # 确保资源被释放
        if pharo_task:
            try:
                pharo_task.browser_controller.close_page()
            except Exception as e:
                pharo_task.logger.debug(f"关闭浏览器失败: {str(e)}")


@cli.command("run")
@click.option("-t", "--type", type=click.Choice(BROWSER_TYPES), default=DEFAULT_BROWSER_TYPE, help="浏览器类型")
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
@click.option("-p", "--proxy-check", is_flag=True, default=PROXY_CHECK, help="是否开启代理检测")
def run(type: str, index: str, workers: int, proxy_check: bool):
    """运行任务

    Args:
        type: 浏览器类型
        index: 浏览器序号
        workers: 并发数量
        proxy_check: 是否开启代理检测
    """
    try:
        # 解析索引
        indices = parse_indices(index)
        random.shuffle(indices)

        # 记录成功和失败的索引
        successful_indices = []
        failed_indices = []
        failed_proxy_ids = []
        failed_discord_ids = []  # 记录绑定失败的Discord账号
        failed_twitter_ids = []  # 记录绑定失败的Twitter账号
        pharo_tasks = {}  # 存储所有任务管理器实例
        active_tasks = set()  # 记录正在执行的任务
        completed_tasks = 0  # 记录已完成的任务数

        @measure_time
        def process_task(index: int) -> bool:
            """处理单个任务

            Args:
                index: 任务索引

            Returns
            -------
                bool: 是否成功
            """
            return execute_single_task(
                type=type,
                index=index,
                pharo_tasks=pharo_tasks,
                active_tasks=active_tasks,
                successful_indices=successful_indices,
                failed_indices=failed_indices,
                failed_proxy_ids=failed_proxy_ids,
                failed_discord_ids=failed_discord_ids,
                failed_twitter_ids=failed_twitter_ids,
                completed_tasks=completed_tasks,
                total_tasks=len(indices),
                proxy_check=proxy_check,
            )

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=TASK_TIMEOUT,
            retries=TASK_RETRIES,
            interval=TASK_INTERVAL,
            task_name=f"PHARO-{type}",
            raise_exception=False,
        )

        try:
            # 批量执行任务
            results = executor.run_batch(process_task, indices)
        except Exception as e:
            logger.error(f"执行任务过程出错: {e}")
        finally:
            # 等待所有活动任务完成或超时
            start_time = time.time()
            while active_tasks and time.time() - start_time < 30:  # 最多等待30秒
                time.sleep(1)

            # 强制关闭所有未完成的任务
            for index in list(active_tasks):
                logger.warning(f"强制关闭任务 {index}")
                if index in pharo_tasks:
                    try:
                        pharo_tasks[index].browser_controller.close_page()
                    except Exception as e:
                        logger.debug(f"关闭浏览器失败: {str(e)}")

        # 处理任务执行结果
        process_task_results(
            results=results,
            indices=indices,
            successful_indices=successful_indices,
            failed_proxy_ids=failed_proxy_ids,
            failed_discord_ids=failed_discord_ids,
            failed_twitter_ids=failed_twitter_ids,
        )

    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


def filter_failed_indices(failed_indices, exceptions):
    """过滤掉包含特定异常的失败索引

    Args:
        failed_indices: 失败索引列表
        exceptions: 需要排除的异常列表

    Returns
    -------
        list: 过滤后的失败索引列表
    """
    filtered_indices = []
    for index in failed_indices:
        if not any(exc in str(index) for exc in exceptions):
            filtered_indices.append(index)
    return filtered_indices


# python3 examples/pharo/giveaway2.py run -t bit -i 1-10
# python3 examples/pharo/giveaway2.py run   -t brave -i 1-100
if __name__ == "__main__":
    cli()
# run_task(BrowserType.CHROME, 1)
