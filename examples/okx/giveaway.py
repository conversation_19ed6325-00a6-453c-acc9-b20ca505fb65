import os
from datetime import datetime
from time import sleep

import click
from loguru import logger

from config import DEFAULT_BROWSER_TYPE
from src.browsers import BROWSER_TYPES, try_click
from src.controllers import BrowserController
from src.enums.browsers_enums import BrowserType
from src.utils import get_element
from src.utils.common import get_project_root_path, parse_indices
from src.utils.data_utils import DataUtil
from src.utils.thread_executor import ThreadExecutor

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]
logger.add("logs/okx.log", rotation="10MB", level="SUCCESS")

TASK_DETAIL_URL = "https://web3.okx.com/priapi/v1/dapp/giveaway/getDetailV2"
TASK_STATUS_URL = "https://web3.okx.com/priapi/v1/dapp/giveaway/task/checkAll"
TASK_CHECK_URL = "https://web3.okx.com/priapi/v1/dapp/giveaway/task/check"
TASK_X_BIND_URL = "https://web3.okx.com/priapi/v1/dapp/oauth2/call-back"

# 登录重试次数
RETRY_COUNT_LOGIN = 6

# 任务重试次数
RETRY_COUNT_TASK = 6



class OKXGiveaway:
    def __init__(self, browser_type: BrowserType, index: str, data_util: DataUtil):
        self.task_name = None
        self.task_url = None
        self.id = index
        self.browser_type = browser_type
        self.browser_controller = BrowserController(browser_type, index)
        self.address = self.browser_controller.browser_config.evm_address
        self.data_util = data_util
        self._init_data()

    def _init_data(self):
        try:
            data = self.data_util.get(self.address)
            if data:
                return

            data = {
                "index": self.id,
                "type": self.browser_type.value,
                "address": self.address,
            }
            self.data_util.add(data)
        except Exception as e:
            logger.error(f"【{self.id}】{self.address} 初始化数据失败: {e}")

    def _setup_browser(self) -> bool:
        """
        设置浏览器环境

        Returns:
            bool: 设置是否成功
        """
        try:
            self.browser_controller.window_max()
            self.browser_controller.okx_wallet_login()
            logger.debug(f"【{self.id}】浏览器环境设置完成")
            return True
        except Exception as e:
            logger.error(f"【{self.id}】浏览器环境设置失败: {e}")
            return False

    def _login(self):
        self._setup_browser()
        for i in range(RETRY_COUNT_LOGIN):
            logger.info(f"【{self.id}】第 {i + 1} 次登录")
            try:
                tab = self.browser_controller.page.new_tab(self.task_url)
                sleep(3)
                if self._is_login(tab):
                    logger.success(f"【{self.id}】已登录")
                    return True

                result = self._connect_wallet(tab)
                if result:
                    return True

                fail_count_str = self.data_util.get(self.address).get("fail_count")
                fail_count = int("0" if fail_count_str in ["", None] else fail_count_str)
                fail_count = fail_count + 1
                self.data_util.update(self.address, {"fail_count": fail_count})
            except Exception as e:
                logger.error(f"【{self.id}】登录异常, error={str(e)}")

        logger.warning(f"【{self.id}】登录重试三次仍未成功, 切换代理")
        return False

    @staticmethod
    def _is_login(tab):
        return get_element(tab, "x://div[@class='wallet-address-container']", 5)

    def _connect_wallet(self, tab):
        if not tab.wait.ele_displayed("x://div[@class='wallet-pc-connect-button connect-wallet-button']", timeout=20):
            logger.error(f"【{self.id}】查找connect-wallet-button超时, 请检查网络是否异常")
            return False
        connect_wallet_btn = get_element(tab, "x://div[@class='wallet-pc-connect-button connect-wallet-button']", 5)
        connect_wallet_btn.click()

        if not tab.wait.ele_displayed("x://button[@class='wallet wallet-btn btn-md btn-fill-highlight']", timeout=20):
            logger.error(f"【{self.id}】查找wallet-btn超时, 请检查网络是否异常")
            return False
        if not try_click(
            tab, "x://button[@class='wallet wallet-btn btn-md btn-fill-highlight']", timeout=10, id=self.id
        ):
            logger.error(f"【{self.id}】多次点击链接钱包无反应")
            return False

        if not self.browser_controller.okx_wallet_connect():
            logger.error(f"【{self.id}】钱包链接失败")
            return False
        sleep(10)
        if self._is_login(tab):
            logger.success(f"【{self.id}】登录成功")
            return True

        logger.error(f"【{self.id}】登录失败")
        return False

    def _link_x(self, task: dict):

        if task["bindStatus"] == 1:
            logger.success(f"【{self.id}】任务【{self.task_name}】推特已绑定")
            self.data_util.update(self.address, {"link_x": "1"})
            return True

        if self.data_util.get(self.address).get("link_x", "") == "1":
            logger.success(f"【{self.id}】任务【{self.task_name}】推特已绑定")
            return True

        if not self.browser_controller.browser_config.x_token and not self.browser_controller.browser_config.x_user:
            logger.warning(f"【{self.id}】推特未配置")
            return False

        for i in range(RETRY_COUNT_TASK):
            logger.info(f"【{self.id}】任务【{self.task_name}】第 {i+1} 次绑定推特")
            tab = None
            try:
                tab = self.browser_controller.page.new_tab(self.task_url)
                name = task["name"]
                task_xpath = f'x://div[text()="{name}"]/ancestor::div[2]'
                if not tab.wait.ele_displayed(task_xpath, timeout=10):
                    logger.warning(f"【{self.id}】任务【{self.task_name}】第 {i+1} 次绑定推特失败，页面未加载...")
                    continue

                # 元素出现，再等待1s元素可以点击
                sleep(1)

                task_div = get_element(tab, task_xpath)
                tab.actions.move_to(task_div)
                sleep(0.5)

                x_connect_btn = get_element(task_div, "x://button[contains(@class, 'index_twitter-connect')]")
                if not x_connect_btn:
                    logger.warning(f"【{self.id}】任务【{self.task_name}】第 {i+1} 次绑定推特失败，未找到绑定按钮")
                    continue

                if not x_connect_btn.states.has_rect:
                    sleep(2)

                x_connect_btn.click()
                if not self.browser_controller.page.wait.new_tab(timeout=20, curr_tab=tab):
                    logger.error(f"【{self.id}】任务【{self.task_name}】第 {i+1} 次绑定推特失败，未打开推特授权页面")
                    continue

                last_tab = self.browser_controller.page.latest_tab
                if "x.com/account/access" in last_tab.url:
                    logger.error(f"【{self.id}】推特被锁定")
                    self.data_util.update(self.address, {"message": "x_is_locked"})
                    return False
                if "login" in last_tab.url:
                    logger.info(f"【{self.id}】需要登录推特")
                    if not self.browser_controller.login_x(False):
                        logger.error(f"【{self.id}】推特登录失败")
                        continue
                if "x.com/i/oauth2/authorize" not in last_tab.url:
                    logger.error(f"【{self.id}】任务【{self.task_name}】第 {i+1} 次绑定推特失败，未跳转到授权页面")
                    continue

                oauth_btn = get_element(last_tab, "x://button[@data-testid='OAuth_Consent_Button']", 20)
                if not oauth_btn:
                    logger.error(f"【{self.id}】任务【{self.task_name}】第 {i+1} 次绑定推特失败, 未找到Twitter授权按钮")
                    continue
                tab.listen.start(TASK_X_BIND_URL)
                oauth_btn.click()
                res = tab.listen.wait(timeout=90)
                if res and res.response.body and res.response.body.get("data", {}).get("bindStatus", 0) == 1:
                    self.data_util.update(self.address, {"link_x": "1"})
                    return True
            except Exception as e:
                logger.error(f"【{self.id}】任务【{self.task_name}】第 {i+1} 次绑定推特失败， error={str(e)}")
            finally:
                if tab:
                    tab.close()
        return False

    def _listen_task(self):
        try:
            logger.info(f"【{self.id}】开始解析【{self.task_name}】的子任务")
            latest_tab = self.browser_controller.page.latest_tab
            targets = [TASK_DETAIL_URL, TASK_STATUS_URL]
            latest_tab.listen.start(targets)
            latest_tab.get(self.task_url)
            sleep(5)
            task_detail = None
            task_status = None
            while not task_detail or not task_status:
                res = latest_tab.listen.wait(timeout=60)
                if not res or not  res.response or not res.response.body:
                    continue
                if TASK_DETAIL_URL in res.request.url:
                    task_detail = res.response.body["data"]
                if TASK_STATUS_URL in res.request.url:
                    task_status = res.response.body["data"]

            # for packet in latest_tab.listen.steps(timeout=60):
            #     if not packet:
            #         logger.debug(f"【{self.id}】开始解析数据返回空")
            #         continue
            #     if TASK_DETAIL_URL in packet.request.url:
            #         task_detail = packet.response.body["data"]
            #     if TASK_STATUS_URL in packet.request.url:
            #         task_status = packet.response.body["data"]
            #     if not task_detail and not task_status:
            #         break
            for task in task_detail["taskList"]:
                task["status"] = task_status.get(str(task["id"]), {}).get("status", 0)
            return task_detail
        except Exception as e:
            logger.error(f"【{self.id}】解析【{self.task_name}】子任务异常，{e}")
            return None

    def _balance(self, task: dict):
        latest_tab = None
        try:
            logger.info(f"【{self.id}】开始检查任务【{self.task_name}】钱包余额")
            if task["status"] == 1:
                self.data_util.update(self.address, {"balance": "1"})
                logger.success(f"【{self.id}】钱包资产大于10U，符合条件")
                return True
            latest_tab = self.browser_controller.page.new_tab(self.task_url)
            latest_tab.listen.start(TASK_CHECK_URL)
            sleep(5)
            name = task["name"]
            refresh_xpath = (
                f'x://div[text()="{name}"]/parent::button/following-sibling::div//i[contains(@class, '
                '"okx-defi-nft-filter-refresh")]'
            )
            refresh = get_element(latest_tab, refresh_xpath)
            refresh.click()
            res = latest_tab.listen.wait(timeout=30)
            if not res or not res.response.body:
                logger.error(f"【{self.id}】验证任务未收到响应")
                return False
            if res.response.body.get("data", {}).get("status", 0) == 0:
                self.data_util.update(self.address, {"balance":"-1"})
                logger.error(f"【{self.id}】钱包资产不足10U，不符合条件")
                return False
            self.data_util.update(self.address, {"balance": "1"})
            logger.success(f"【{self.id}】钱包资产大于10U，符合条件")
            return True
        except Exception as e:
            logger.error(f"【{self.id}】检查钱包余额异常, error={str(e)}")
        finally:
            if latest_tab:
                latest_tab.close()
        return False

    def _execute(self, task: dict):
        logger.info(f"【{self.id}】开始任务【{self.task_name}】-【{task['name']}】")

        if self.data_util.get(self.address).get(f"{task['id']}", "0") == "1":
            logger.success(f"【{self.id}】任务【{self.task_name}】-【{task['name']}】已完成")
            return True

        if task["status"] == 1:
            self.data_util.update(self.address, {f"{task['id']}": "1"})
            logger.success(f"【{self.id}】任务【{self.task_name}】-【{task['name']}】已完成")
            return True

        # if task["playType"] == 0 and task["taskType"] == 0:
        #     logger.info(f"【{self.id}】请确保【{self.task_name}】-【{task['name']}】官网任务已完成")

        for i in range(RETRY_COUNT_TASK):
            logger.info(f"【{self.id}】第 {i+1} 次执行任务【{self.task_name}】-【{task['name']}】")
            tab = None
            try:
                tab = self.browser_controller.page.new_tab(self.task_url)
                name = task["name"]
                if not tab.wait.ele_displayed(f'x://div[text()="{name}"]', timeout=10):
                    logger.warning(
                        f"【{self.id}】第 {i+1} 次执行任务【{self.task_name}】-【{task['name']}】失败，页面未加载..."
                    )
                    continue

                # 元素出现，再等待1s元素可以点击
                sleep(1)
                tab.actions.move_to(f'x://div[text()="{name}"]')
                sleep(0.5)

                # 关注推特任务多等待
                if task["playType"] == 1 and task["taskType"] == 1:
                    sleep(5)

                task_div_xpath = f'x://div[text()="{name}"]/ancestor::div[2]'
                task_div = get_element(tab, task_div_xpath)

                start_xpath = ""
                if task["playType"] == 1 and task["taskType"] == 1:
                    start_xpath = "x://button[contains(@class, 'index_start_')]"
                else:
                    start_xpath = "x://div[contains(@class, 'index_common-task')]/button"

                start_btn = get_element(task_div, start_xpath)

                if start_btn and start_btn.states.has_rect:
                    start_btn.click()
                    if self.browser_controller.page.wait.new_tab(timeout=10, curr_tab=tab):
                        sleep(1)
                        self.browser_controller.page.close_tabs([self.browser_controller.page.latest_tab])
                        sleep(1)
                    sleep(3)

                if get_element(task_div, "x://i[contains(@class, 'okds-success-circle-fill')]", 5):
                    logger.success(f"【{self.id}】任务【{self.task_name}】-【{task['name']}】完成")
                    self.data_util.update(self.address, {f"{task['id']}": "1"})
                    return True

                refresh_xpath = f'x://i[contains(@class, "okx-defi-nft-filter-refresh")]'
                refresh = get_element(task_div, refresh_xpath)
                if refresh:
                    tab.listen.start(TASK_CHECK_URL)
                    refresh.click()
                    res = tab.listen.wait(timeout=30)
                    if res and res.response.body and res.response.body.get("data", {}).get("status", 0) == 1:
                        logger.success(f"【{self.id}】任务【{self.task_name}】-【{task['name']}】完成")
                        self.data_util.update(self.address, {f"{task['id']}": "1"})
                        return True
            except Exception as e:
                logger.error(f"【{self.id}】任务【{self.task_name}】-【{task['name']}】异常, error={str(e)}")
            finally:
                if tab:
                    tab.close()

        return False

    def _verify(self) -> bool:
        try:
            logger.info(f"【{self.id}】开始验证任务【{self.task_name}】")
            latest_tab = self.browser_controller.page.latest_tab
            verify_btn = get_element(latest_tab, "x://button[contains(@class, 'index_bottom-btn')]")
            if not verify_btn:
                logger.warning(f"【{self.id}】未找到任务【{self.task_name}】验证按钮")
                return False

            if verify_btn.states.is_enabled:
                latest_tab.listen.start("https://web3.okx.com/priapi/v1/dapp/giveaway/claimFinished")
                verify_btn.click()
                res = latest_tab.listen.wait(timeout=30)
                if res and res.response.body and res.response.body.get("data", {}).get("verifySucceed", False):
                    logger.success(f"【{self.id}】任务【{self.task_name}】完成，等待结果")
                    self.data_util.update(self.address, {"status": "1"})
                    return True
                else:
                    logger.warning(f"【{self.id}】任务【{self.task_name}】还有未完成子任务")
                    return False
            else:
                logger.success(f"【{self.id}】任务【{self.task_name}】完成，等待结果")
                self.data_util.update(self.address, {"status": "1"})
                return True
        except Exception as e:
            logger.error(f"【{self.id}】验证任务【{self.task_name}】发生异常, error={str(e)}")
            return False

    def task(self, task_name: str, task_url: str) -> bool:
        self.task_name = task_name
        self.task_url = task_url
        try:
            if self.data_util.get(self.address).get("status", "0") == "1":
                logger.success(f"【{self.id}】任务【{self.task_name}】已全部完成")
                return True

            if self.data_util.get(self.address).get("status", "0") == "-1":
                logger.warning(f"【{self.id}】任务【{self.task_name}】已结束")
                return True

            if not self._login():
                logger.error(f"【{self.id}】执行任务【{self.task_name}】登录失败")
                return False

            detail = self._listen_task()
            if not detail:
                logger.error(f"【{self.id}】执行任务【{self.task_name}】失败，未解析到任务数据")
                return False

            if detail["verified"]:
                logger.success(f"【{self.id}】任务【{self.task_name}】已全部完成")
                self.data_util.update(self.address, {"status": "1"})
                return True
            else:
                self.data_util.update(self.address, {"status": "0"})

            if detail["giveawayStatus"] == 1:
                logger.success(f"【{self.id}】任务【{self.task_name}】已全部完成")
                self.data_util.update(self.address, {"status": "1"})
                return True

            end_time = detail["endTime"]
            local_time = detail["localTime"]
            if self._is_end(end_time, local_time):
                logger.success(f"【{self.id}】任务【{self.task_name}】已结束")
                self.data_util.update(self.address, {"status": "-1"})
                return True
            tasks = sorted(detail["taskList"], key=lambda x: x["id"])
            logger.info(f"【{self.id}】任务【{self.task_name}】共 {len(tasks)} 个子任务")
            if not self._balance(tasks[len(tasks) - 1]):
                logger.error(f"【{self.id}】任务【{self.task_name}】余额不足, 跳过执行...")
                return False

            for i, task in enumerate(tasks):
                if task["status"] == 1:
                    logger.success(f"【{self.id}】任务【{self.task_name}】-【{task['name']}】已完成")
                    continue

                if i == 0 and not self._link_x(task):
                    logger.success(f"【{self.id}】任务【{self.task_name}】绑定X失败")
                    return False

                if not self._execute(task):
                    logger.warning(f"【{self.id}】任务【{self.task_name}】-【{task['name']}】未完成")

            return self._verify()
        except Exception as e:
            logger.error(f"【{self.id}】执行任务【{self.task_name}】失败, error={str(e)}")
            return False
        finally:
            if self.browser_controller:
                self.browser_controller.close_page()

    def check(self, task_name: str, task_url: str) -> bool:
        self.task_name = task_name
        self.task_url = task_url
        try:
            # if self.data_util.get(self.address).get("status", "0") != "1":
            #     logger.warning(f"【{self.id}】任务【{self.task_name}】未完成")
            #     self.data_util.update(self.address, {"win": "0"})
            #     return True
            #
            # if not self._login():
            #     logger.error(f"【{self.id}】执行任务【{self.task_name}】登录失败")
            #     return False
            #
            # detail = self._listen_task()
            # if not detail:
            #     logger.error(f"【{self.id}】执行任务【{self.task_name}】失败，未解析到任务数据")
            #     return False
            #
            # if detail["winStatus"] == 1:
            #     logger.success(f"【{self.id}】任务【{self.task_name}】中奖")
            #     self.data_util.update(self.address, {"win": "1"})
            # else:
            #     logger.info(f"【{self.id}】任务【{self.task_name}】未中奖")
            #     self.data_util.update(self.address, {"win": "0"})
            return True
        except Exception as e:
            logger.error(f"【{self.id}】执行任务【{self.task_name}】失败, error={str(e)}")
            return False
        finally:
            if self.browser_controller:
                self.browser_controller.close_page()

    @staticmethod
    def _is_end(end_time_ms, local_time_ms):
        end_time = datetime.fromtimestamp(end_time_ms / 1000)
        local_time = datetime.fromtimestamp(local_time_ms / 1000)
        return local_time > end_time


def _get_data_util(browser_type: BrowserType, task_name: str) -> DataUtil:
    """
    获取数据工具实例

    Returns:
        DataUtil: 数据工具实例
    """

    data_dir = os.path.join(get_project_root_path(), "examples", "okx")
    csv_path = os.path.join(data_dir, f"giveaway_{task_name}_{browser_type.name.lower()}.csv")
    return DataUtil(csv_path)


@click.group()
def cli():
    pass


@cli.command("run")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
def run(type, index, workers):
    tasks = [
        {
            "task_name": "Jasper Vault 价值 50K USDT 代币奖励活动",
            "task_url": "https://web3.okx.com/zh-hans/giveaway/jaspervault",
        },
        # {
        #     "task_name": "ShareX 价值 10 万美元寻宝活动 TreasureX",
        #     "task_url": "https://web3.okx.com/zh-hans/giveaway/sharex",
        # },
        # {
        #     "task_name": "价值 4 万美元 ATRM 代币奖励活动",
        #     "task_url": "https://web3.okx.com/zh-hans/giveaway/alterimai",
        # },
    ]
    for task in tasks:
        try:
            indices = parse_indices(index)
            data_util = _get_data_util(type, task["task_name"])

            successful_indices = []
            failed_indices = []

            separator = "=" * 20
            logger.info(f"{separator}开始执行任务【{task['task_name']}】{separator}")

            executed_indices = [int(data["index"]) for data in data_util.list() if data.get("status", "0") == "1"]

            # 从全部任务中去除已经执行成功的的任务
            execute_indices = [x for x in indices if x not in executed_indices]

            if len(execute_indices) == 0:
                logger.success(f"【{task['task_name']}】所有账号均已完成")
                continue

            def process_task(idx):
                giveaway = OKXGiveaway(type, idx, data_util)
                result = giveaway.task(task["task_name"], task["task_url"])
                if result:
                    successful_indices.append(idx)
                else:
                    failed_indices.append(idx)
                return result

            # 创建线程执行器
            executor = ThreadExecutor(
                workers=workers,
                timeout=6 * 3600,  # 6小时超时
                retries=3,
                interval=10,
                task_name=f"giveaway_{type}",
                raise_exception=False,
            )

            # 批量执行任务
            executor.run_batch(process_task, execute_indices)
            # 计算失败列表
            failed_indices = list(set(execute_indices) - set(successful_indices))  # 计算失败的索引
            # 如果需要保持列表格式
            failed_indices = [str(index) for index in failed_indices]
            logger.success(
                f"【{task['task_name']}】本次共执行 {len(execute_indices)} 个账号，成功 {len(successful_indices)} 个，失败"
                f" {len(failed_indices)} 个"
            )
            if len(failed_indices) > 0:
                logger.success(f"【{task['task_name']}】失败的账号列表: {','.join(failed_indices)}")

        except Exception as e:
            logger.error(f"【{task['task_name']}】发生异常: {e}")


@cli.command("check")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
def check(type, index, workers):
    tasks = [
        # {
        #     "task_name": "Jasper Vault 价值 50K USDT 代币奖励活动",
        #     "task_url": "https://web3.okx.com/zh-hans/giveaway/jaspervault",
        # },
        {
            "task_name": "ShareX 价值 10 万美元寻宝活动 TreasureX",
            "task_url": "https://web3.okx.com/zh-hans/giveaway/sharex",
        },
        # {
        #     "task_name": "价值 4 万美元 ATRM 代币奖励活动",
        #     "task_url": "https://web3.okx.com/zh-hans/giveaway/alterimai",
        # },
    ]
    for task in tasks:
        try:
            indices = parse_indices(index)
            data_util = _get_data_util(type, task["task_name"])

            successful_indices = []
            failed_indices = []

            separator = "=" * 20
            logger.info(f"{separator}开始查询【{task['task_name']}】{separator}")

            def process_task(idx):
                giveaway = OKXGiveaway(type, idx, data_util)
                result = giveaway.check(task["task_name"], task["task_url"])
                if result:
                    successful_indices.append(idx)
                else:
                    failed_indices.append(idx)
                return result

            # 创建线程执行器
            executor = ThreadExecutor(
                workers=workers,
                timeout=6 * 3600,  # 6小时超时
                retries=3,
                interval=10,
                task_name=f"giveaway_{type}",
                raise_exception=False,
            )

            # 批量执行任务
            executor.run_batch(process_task, indices)
            # 计算失败列表
            failed_indices = list(set(indices) - set(successful_indices))  # 计算失败的索引
            # 如果需要保持列表格式
            failed_indices = [str(index) for index in failed_indices]
            logger.success(
                f"【{task['task_name']}】本次共执行 {len(indices)} 个账号，成功 {len(successful_indices)} 个，失败"
                f" {len(failed_indices)} 个"
            )
            if len(failed_indices) > 0:
                logger.success(f"【{task['task_name']}】失败的账号列表: {','.join(failed_indices)}")

        except Exception as e:
            logger.error(f"【{task['task_name']}】发生异常: {e}")


# python3 examples/okx/giveaway.py run -t ads -i 1-100 -w 3
if __name__ == "__main__":
    cli()
