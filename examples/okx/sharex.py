import os
import random
from time import sleep

import click
from loguru import logger

from config import DEFAULT_BROWSER_TYPE
from src.browsers import BROWSER_TYPES
from src.controllers import BrowserController
from src.enums.browsers_enums import BrowserType
from src.utils import get_element
from src.utils.common import get_project_root_path, parse_indices
from src.utils.data_utils import DataUtil
from src.utils.thread_executor import ThreadExecutor

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]
logger.add("logs/sharex.log", rotation="10MB", level="SUCCESS")


TASK_URL = "https://treasurex.sharex.network/giveaway/welcome?platformType=okx&invite=666"

# 登录重试次数
RETRY_COUNT_LOGIN = 6

# 任务重试次数
RETRY_COUNT_TASK = 6


class ShareX:
    def __init__(self, browser_type: BrowserType, index: str, data_util: DataUtil):
        self.id = index
        self.browser_type = browser_type
        self.browser_controller = BrowserController(browser_type, index)
        self.address = self.browser_controller.browser_config.evm_address
        self.data_util = data_util
        self._init_data()

    def _init_data(self):
        try:
            data = self.data_util.get(self.address)
            if data:
                return

            data = {
                "index": self.id,
                "type": self.browser_type.value,
                "address": self.address,
            }
            self.data_util.add(data)
        except Exception as e:
            logger.error(f"【{self.id}】{self.address} 初始化数据失败: {e}")

    def _setup_browser(self) -> bool:
        """
        设置浏览器环境

        Returns:
            bool: 设置是否成功
        """
        try:
            self.browser_controller.window_max()
            self.browser_controller.okx_wallet_login()
            logger.debug(f"【{self.id}】浏览器环境设置完成")
            return True
        except Exception as e:
            logger.error(f"【{self.id}】浏览器环境设置失败: {e}")
            return False

    def _login(self):
        self._setup_browser()
        for i in range(RETRY_COUNT_LOGIN):
            logger.info(f"【{self.id}】第 {i + 1} 次登录")
            latest_tab = None
            try:
                latest_tab = self.browser_controller.page.new_tab()
                latest_tab.listen.start("https://treasurex.sharex.network/sharex-activity-api/wallet/login")
                latest_tab.get(TASK_URL)

                btn = get_element(latest_tab, "x://button", 5)
                if not btn:
                    logger.warning(f"【{self.id}】未找到Claim按钮")
                    continue

                btn.click()
                if not self.browser_controller.page.wait.new_tab(timeout=10, curr_tab=latest_tab):
                    logger.warning(f"【{self.id}】点击Claim按钮无反应")
                    continue

                try:
                    self.browser_controller.okx_wallet_connect()
                except:
                    pass

                res = latest_tab.listen.wait(timeout=15)
                if res and res.response.body and res.response.body.get("code") == "SUCCESS":
                    logger.success(f"【{self.id}】登录成功")
                    return True

            except Exception as e:
                logger.error(f"【{self.id}】登录异常, error={str(e)}")
            finally:
                latest_tab.close()

        logger.error(f"【{self.id}】登录重试{RETRY_COUNT_LOGIN}次仍未成功")
        return False

    def task(self) -> bool:
        try:
            if self.data_util.get(self.address).get("status", "0") == "1":
                logger.success(f"【{self.id}】已完成任务")
                return True

            tab = self.browser_controller.page.new_tab(TASK_URL)
            sleep(5)
            if tab.url == "https://treasurex.sharex.network/giveaway/":
                self.data_util.update(self.address, {"status": "1"})
                logger.success(f"【{self.id}】已完成任务")
                return True

            if self._login():
                self.data_util.update(self.address, {"status": "1"})
                logger.success(f"【{self.id}】已完成任务")
                return True

            return False
        except Exception as e:
            logger.error(f"【{self.id}】执行失败, error={str(e)}")
            return False
        finally:
            if self.browser_controller:
                self.browser_controller.close_page()


def _get_data_util(
    browser_type: BrowserType,
) -> DataUtil:
    """
    获取数据工具实例

    Returns:
        DataUtil: 数据工具实例
    """

    data_dir = os.path.join(get_project_root_path(), "examples", "okx")
    csv_path = os.path.join(data_dir, f"sharex_{browser_type.name.lower()}.csv")
    return DataUtil(csv_path)


@click.group()
def cli():
    pass


@cli.command("run")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
    help="浏览器类型",
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-w", "--workers", type=int, default=1, help="并发数量")
def run(type, index, workers):
    try:
        indices = parse_indices(index)
        random.shuffle(indices)
        successful_indices = []  # 新增成功列表

        data_util = _get_data_util(type)

        executed_indices = [int(data["index"]) for data in data_util.list() if data["status"] == "1"]

        # 从全部任务中去除已经执行成功的的任务
        execute_indices = [x for x in indices if x not in executed_indices]

        if len(execute_indices) == 0:
            logger.success("所有任务全部执行完成")
            return True

        def process_task(idx: int) -> bool:
            sharex = None
            try:
                sharex = ShareX(type, str(idx), data_util)
                if sharex.data_util.get(sharex.address).get("status", "") == "1":
                    logger.info(f"【{idx}】已完成全部任务")
                    successful_indices.append(idx)
                    return True
                result = sharex.task()
                if result:
                    successful_indices.append(idx)
                return result
            except Exception as e:
                logger.error(f"【{idx}】执行任务异常: {str(e)}")
                return False
            finally:
                if sharex:
                    sharex.browser_controller.close_page()

        # 创建线程执行器
        executor = ThreadExecutor(
            workers=workers,
            timeout=6 * 3600,  # 6小时超时
            retries=3,
            interval=10,
            task_name=f"ShareX_{type}",
            raise_exception=False,
        )

        # 批量执行任务
        executor.run_batch(process_task, execute_indices)
        # 计算失败列表
        failed_indices = list(set(execute_indices) - set(successful_indices))  # 计算失败的索引
        # 如果需要保持列表格式
        failed_indices = [str(index) for index in failed_indices]
        logger.success(
            f"本次共执行 {len(execute_indices)} 个账号，成功 {len(successful_indices)} 个，失败 {len(failed_indices)} 个"
        )
        if len(failed_indices) > 0:
            logger.success(f"失败的账号列表: {','.join(failed_indices)}")
    except Exception as e:
        logger.error(f"执行任务过程出错: {e}")
        raise


# python3 examples/okx/giveaway.py run -t ads -i 1-100 -w 3
if __name__ == "__main__":
    cli()
