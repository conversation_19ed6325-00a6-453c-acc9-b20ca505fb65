from time import sleep

import click
from loguru import logger

from config import DEFAULT_BROWSER_TYPE
from src.browsers import BROWSER_TYPES, BrowserType
from src.controllers import BrowserController
from src.utils.common import parse_indices
from utils import process_input_file

default_browser_type = BrowserType[DEFAULT_BROWSER_TYPE]


def _okx_wallet_setup(index, type, is_key: bool = False, name: str | None = None):
    indices = parse_indices(index)
    for _index in indices:
        try:
            browser = BrowserController(type, str(_index))
            result = browser.okx_wallet_setup(is_key=is_key, wallet_name=name)
            if result:
                sleep(3)
                browser.close_page()
        except Exception as e:
            logger.error(f"{_index} okx 导入钱包异常 {e}")
        # finally:
        #     if browser:
        #         browser.close_page()


def _okx_wallet_reset(index, type):
    indices = parse_indices(index)
    for _index in indices:
        try:
            browser = BrowserController(type, str(_index))
            result = browser.okx_wallet_reset()
            if result:
                sleep(3)
                browser.close_page()
        except Exception as e:
            logger.error(f"{_index} okx 导入钱包异常 {e}")
        # finally:
        #     if browser:
        #         browser.close_page()


def _okx_remove_custom_rpc(index, type, name):
    indices = parse_indices(index)
    for _index in indices:
        browser = None
        try:
            browser = BrowserController(type, str(_index))
            browser.okx_wallet_login()
            result = browser.okx_wallet_remove_custom_rpc(name)
            if result:
                sleep(3)
                browser.close_page()
        except Exception as e:
            logger.error(f"{_index} okx 移除自定义RPC异常 {e}")
        finally:
            if browser:
                browser.close_page()


@click.group()
def cli():
    pass


@cli.command("okx")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def okx(index, type):
    indices = parse_indices(index)
    for _index in indices:
        try:
            BrowserController(type, str(_index)).okx_wallet_login()
        except Exception as e:
            logger.error(f"{_index} okx 登录异常 {e}")


@cli.command("okxi")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-k", "--is_key", is_flag=True, default=False, help="是否使用私钥导入")
@click.option("-n", "--name", type=str, help="钱包名称", default=None)
def okx_import(index, type, is_key, name):
    _okx_wallet_setup(index, type, is_key, name)


@cli.command("okxr")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def okx_reset(index, type):
    _okx_wallet_reset(index, type)


@cli.command("okxrr")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
@click.option("-n", "--name", type=str, help="钱包名称", default=None)
def okx_remove_custom_rpc(index, type, name):
    _okx_remove_custom_rpc(index, type, name)


@cli.command("mm")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def metamask(index, type):
    indices = parse_indices(index)
    for _index in indices:
        try:
            browser = BrowserController(type, str(_index))
            browser.metamask_wallet_login()
        except Exception as e:
            logger.error(f"{_index} metamask 钱包登录异常 {e}")


@cli.command("mmi")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def metamask_import(index, type):
    indices = parse_indices(index)
    for _index in indices:
        browser = None
        try:
            browser = BrowserController(type, str(_index))
            result = browser.metamask_wallet_setup()
            if result:
                browser.close_page()
        except Exception as e:
            logger.error(f"{_index} metamask 导入钱包异常 {e}")


@cli.command("bpi")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def backpack_import(index, type):
    required_cols = [
        "id",
        "browser_id",
        "mnemonic",
    ]

    data = process_input_file(required_cols=required_cols, index=index)
    if data is None:
        return

    for row in data:
        browser = None
        try:
            id = row.get("id")
            mnemonic = row.get("mnemonic")
            browser = BrowserController(type, str(id))
            result = browser.backpack_wallet_setup(mnemonic)
            if result:
                browser.close_page()
        except Exception as e:
            logger.error(f"{id} backpack 导入钱包异常 {e}")


@cli.command("bp")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def backpack_login(index, type):
    indices = parse_indices(index)
    for _index in indices:
        try:
            browser = BrowserController(type, str(_index))
            browser.backpack_wallet_login()
        except Exception as e:
            logger.error(f"{_index} backpack 登录异常 {e}")


@cli.command("kpi")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def keplr_import(index, type):
    required_cols = [
        "id",
        "browser_id",
        "mnemonic",
    ]

    data = process_input_file(required_cols=required_cols, index=index)
    if data is None:
        return

    for row in data:
        browser = None
        try:
            id = row.get("id")
            mnemonic = row.get("mnemonic")
            browser = BrowserController(type, str(id))
            result = browser.keplr_wallet_setup(mnemonic)
            if result:
                browser.close_page()
        except Exception as e:
            logger.error(f"{id} keplr 导入钱包异常 {e}")


@cli.command("kp")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def keplr_login(index, type):
    indices = parse_indices(index)
    for _index in indices:
        try:
            browser = BrowserController(type, str(_index))
            browser.keplr_wallet_login()
        except Exception as e:
            logger.error(f"{_index} keplr 登录异常 {e}")


@cli.command("razori")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def razor_import(index, type):
    required_cols = [
        "id",
        "browser_id",
        "mnemonic",
    ]

    data = process_input_file(required_cols=required_cols, index=index)
    if data is None:
        return

    for row in data:
        browser = None
        try:
            id = row.get("id")
            mnemonic = row.get("mnemonic")
            browser = BrowserController(type, str(id))
            result = browser.razor_wallet_setup(mnemonic)
            if result:
                browser.close_page()
        except Exception as e:
            logger.error(f"{id} razor 导入钱包异常 {e}")


@cli.command("razor")
@click.option(
    "-t",
    "--type",
    type=click.Choice(BROWSER_TYPES),
    default=default_browser_type,
)
@click.option("-i", "--index", type=str, prompt="请输入浏览器序号", help="浏览器序号")
def razor_login(index, type):
    indices = parse_indices(index)
    for _index in indices:
        try:
            browser = BrowserController(type, str(_index))
            browser.razor_wallet_login()
        except Exception as e:
            logger.error(f"{_index} razor 登录异常 {e}")


# 导入OKX钱包
# python giveaway2.py okxi -i 1-10

# 登录okx
# python giveaway2.py okx -i 1-10

# 导入metamask钱包
# python giveaway2.py mmi -i 1-10

# 登录metamask钱包
# python giveaway2.py mm -i 1-10

# 导入backpack钱包
# python giveaway2.py bpi -i 1-10

# 登录backpack钱包
# python giveaway2.py bp -i 1-10

if __name__ == "__main__":
    cli()

    # index = "1"
    # _okx_wallet_setup(index, BrowserType.CHROME, True, index)

    # backpack_import(index="1", type=BrowserType.BIT)
    # backpack_login(index="1", type=BrowserType.BIT)
